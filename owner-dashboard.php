<?php
// === 1. เริ่มต้นและเชื่อมต่อฐานข้อมูล ===
session_start();
require('connect.php'); // ต้องมีไฟล์นี้เชื่อมต่อฐานข้อมูล

// --- จัดการ Action ทั้งหมด ---
$action = isset($_GET['action']) ? $_GET['action'] : null;
// ***ปรับปรุง: กำหนดหน้าเริ่มต้นเป็น approve_booking หากไม่มีการระบุ page***
$currentPage = isset($_GET['page']) ? $_GET['page'] : 'approve_booking';

// ACTION: สร้างใบจองใหม่
if ($action == 'create_booking' && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $room_id = (int)$_POST['room_id'];
    $customer_name = $_POST['customer_name'];
    $customer_phone = $_POST['customer_phone'];
    $check_in = $_POST['check_in_date'];
    $check_out = $_POST['check_out_date'];

    $stmt = $connect->prepare("INSERT INTO bookings (room_id, customer_name, customer_phone, check_in_date, check_out_date, booking_status, created_at) VALUES (?, ?, ?, ?, ?, 'Pending', NOW())");
    $stmt->bind_param("issss", $room_id, $customer_name, $customer_phone, $check_in, $check_out);
    $stmt->execute();
    $_SESSION['message'] = "ส่งคำขอจองเรียบร้อยแล้ว กรุณารอการอนุมัติ";
    header('Location: owner-dashboard.php?page=booking');
    exit;
}

// ACTION: อนุมัติการจอง
if ($action == 'approve' && isset($_GET['booking_id']) && isset($_GET['room_id'])) {
    $booking_id = (int)$_GET['booking_id'];
    $room_id = (int)$_GET['room_id'];
    $connect->begin_transaction();
    try {
        $connect->query("UPDATE rooms SET status = 'ไม่ว่าง' WHERE id = $room_id");
        $connect->query("UPDATE bookings SET booking_status = 'Approved' WHERE id = $booking_id");
        $connect->commit();
        $_SESSION['message'] = "อนุมัติการจองเรียบร้อยแล้ว!";
    } catch (Exception $e) {
        $connect->rollback();
        $_SESSION['message'] = "เกิดข้อผิดพลาด: " . $e->getMessage();
    }
    header('Location: owner-dashboard.php?page=approve_booking');
    exit;
}

// ACTION: ปฏิเสธการจอง
if ($action == 'reject' && isset($_GET['booking_id'])) {
    $booking_id = (int)$_GET['booking_id'];
    $connect->query("UPDATE bookings SET booking_status = 'Rejected' WHERE id = $booking_id");
    $_SESSION['message'] = "ปฏิเสธการจองเรียบร้อยแล้ว";
    header('Location: owner-dashboard.php?page=approve_booking');
    exit;
}

// ACTION: คืนห้องพัก
if ($action == 'check_out' && isset($_GET['booking_id']) && isset($_GET['room_id'])) {
    $booking_id = (int)$_GET['booking_id'];
    $room_id = (int)$_GET['room_id'];
    $connect->begin_transaction();
    try {
        $connect->query("UPDATE rooms SET status = 'ว่าง' WHERE id = $room_id");
        $connect->query("UPDATE bookings SET booking_status = 'Checked-out' WHERE id = $booking_id");
        $connect->commit();
        $_SESSION['message'] = "คืนห้องพักเรียบร้อยแล้ว!";
    } catch (Exception $e) {
        $connect->rollback();
        $_SESSION['message'] = "เกิดข้อผิดพลาด: " . $e->getMessage();
    }
    header('Location: owner-dashboard.php?page=return_room');
    exit;
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - ระบบจัดการรีสอร์ท</title>
    <!-- ใส่ Link CSS และ Font ของคุณที่นี่ -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Sarabun:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* CSS ทั้งหมดของคุณ (เหมือนเดิม) */
        :root {
            --primary-color: #7b61ff; --primary-light: #f0edff; --primary-dark: #5c48d3;
            --secondary-color: #f8f9fa; --border-color-light: #e9ecef;
            --text-color: #333; --text-secondary: #6c757d; --bg-color: #f7f7fc;
            --sidebar-bg: #ffffff; --border-color: #e9e9f0; --sidebar-width: 260px;
            --green-color: #198754; --red-color: #dc3545; --orange-color: #fd7e14;
            --info-color: #0dcaf0;
        }
        body { font-family: 'Sarabun', sans-serif; margin: 0; background-color: var(--bg-color); }
        .main-content { margin-left: var(--sidebar-width); padding: 30px; }
        .sidebar { width: var(--sidebar-width); height: 100vh; position: fixed; top: 0; left: 0; background-color: var(--sidebar-bg); padding: 20px 15px; display: flex; flex-direction: column; border-right: 1px solid var(--border-color); box-sizing: border-box; }
        .sidebar-header { display: flex; align-items: center; padding: 0 10px 20px 10px; margin-bottom: 20px; border-bottom: 1px solid var(--border-color); }
        .logo-icon { font-size: 28px; color: var(--primary-color); margin-right: 12px; }
        .logo-text { font-size: 20px; font-weight: 700; color: var(--text-color); }
        .sidebar-nav { list-style: none; padding: 0; margin: 0; flex-grow: 1; }
        .nav-header { padding: 25px 10px 10px 10px; font-size: 13px; font-weight: 600; color: var(--text-secondary); text-transform: uppercase; }
        .sidebar-nav li a { display: flex; align-items: center; padding: 12px 15px; margin: 4px 0; color: var(--text-secondary); text-decoration: none; font-size: 15px; font-weight: 500; border-radius: 8px; transition: background-color 0.2s, color 0.2s; }
        .sidebar-nav li a i { width: 22px; margin-right: 18px; font-size: 18px; text-align: center; }
        /* *** สไตล์สำหรับเมนูที่ Active *** */
        .sidebar-nav li.active a { background-color: var(--primary-color); color: #ffffff; font-weight: 600; }
        .sidebar-nav li a:hover:not(.active) { background-color: var(--primary-light); color: var(--primary-dark); }
        
        .page-header h1 { margin: 0 0 30px 0; font-size: 28px; }
        .btn { padding: 8px 15px; border-radius: 6px; font-size: 14px; font-weight: 600; cursor: pointer; text-decoration: none; display: inline-flex; align-items: center; gap: 8px; border: none; transition: opacity 0.2s; }
        .btn:hover { opacity: 0.8; }
        .btn-primary { background-color: var(--primary-color); color: white; }
        .btn-success { background-color: var(--green-color); color: white; }
        .btn-danger { background-color: var(--red-color); color: white; }
        .btn-info { background-color: var(--info-color); color: white; }
        
        .table-container { background-color: #fff; border-radius: 12px; padding: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.06); overflow-x: auto;}
        .styled-table { border-collapse: collapse; width: 100%; font-size: 15px; min-width: 800px; }
        .styled-table thead tr { background-color: #f8f9fa; color: #333; text-align: left; }
        .styled-table th, .styled-table td { padding: 15px; }
        .styled-table tbody tr { border-bottom: 1px solid #f0f0f0; }
        .styled-table tbody tr:last-of-type { border-bottom: none; }
        
        .status-pending { color: var(--orange-color); font-weight: bold; }
        .status-approved { color: var(--green-color); font-weight: bold; }
        .status-rejected { color: var(--red-color); font-weight: bold; }

        .form-container { max-width: 600px; background-color: #fff; padding: 30px; border-radius: 12px; box-shadow: 0 4px 15px rgba(0,0,0,0.06); }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 8px; font-weight: 600; }
        .form-group input { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px; box-sizing: border-box; }

        .alert-message { padding:15px; background-color:#d1e7dd; color:#0f5132; border:1px solid #badbcc; border-radius:8px; margin-bottom:20px; }
    </style>
</head>
<body>

    <!-- ======================= เพิ่มส่วนนี้เข้าไป ======================= -->
    <aside class="sidebar">
        <div class="sidebar-header">
            <i class="fas fa-hotel logo-icon"></i>
            <span class="logo-text">Resort owner</span>
        </div>
        <ul class="sidebar-nav">
            <li class="nav-header">จัดการการจอง</li>

            <!-- *** นี่คือส่วนสำคัญ: เราจะเพิ่ม class="active" โดยใช้ PHP *** -->
            <li class="<?php echo ($currentPage == 'approve_booking') ? 'active' : ''; ?>">
                <a href="owner-dashboard.php?page=approve_booking">
                    <i class="fa-solid fa-check-to-slot"></i>
                    <span>อนุมัติการจอง</span>
                </a>
            </li>

            <li class="<?php echo ($currentPage == 'return_room') ? 'active' : ''; ?>">
                <a href="owner-dashboard.php?page=return_room">
                    <i class="fa-solid fa-person-walking-arrow-right"></i>
                    <span>คืนห้องพัก</span>
                </a>
            </li>

            <li class="nav-header">สำหรับลูกค้า Walk-in</li>
            
            <li class="<?php echo ($currentPage == 'booking' || $currentPage == 'booking_form') ? 'active' : ''; ?>">
                <a href="owner-dashboard.php?page=booking">
                    <i class="fa-solid fa-plus"></i>
                    <span>จองห้องพัก</span>
                </a>
            </li>
        </ul>
    </aside>
    <!-- ======================= สิ้นสุดส่วนที่เพิ่ม ======================= -->
    
    <main class="main-content">
<?php 
if (isset($_SESSION['message'])) {
    echo '<div class="alert-message">' . htmlspecialchars($_SESSION['message']) . '</div>';
    unset($_SESSION['message']);
}

// --- ส่วนแสดงเนื้อหาตามหน้า ---
if ($currentPage == 'approve_booking') {
    // === เพิ่มส่วนนี้: ดึงข้อมูลการจองที่รออนุมัติ ===
    $pending_bookings = $connect->query("
        SELECT b.id AS booking_id, b.room_id, r.name AS room_name, b.customer_name, 
               b.check_in_date, b.check_out_date, b.created_at
        FROM bookings b 
        JOIN rooms r ON b.room_id = r.id
        WHERE b.booking_status = 'Pending'
        ORDER BY b.created_at ASC
    ");
    ?>
    <div class="page-header"><h1>รายการคำขอจองที่รออนุมัติ</h1></div>
    <div class="table-container">
        <table class="styled-table">
            <thead>
                <tr>
                    <th>ห้อง</th>
                    <th>ชื่อผู้จอง</th>
                    <th>วันที่เข้าพัก</th>
                    <th>วันที่ออก</th>
                    <th>จัดการ</th>
                </tr>
            </thead>
            <tbody>
            <?php if($pending_bookings->num_rows > 0): ?>
                <?php while($booking = $pending_bookings->fetch_assoc()): ?>
                <tr>
                    <td><?php echo htmlspecialchars($booking['room_name']); ?></td>
                    <td><?php echo htmlspecialchars($booking['customer_name']); ?></td>
                    <td><?php echo htmlspecialchars($booking['check_in_date']); ?></td>
                    <td><?php echo htmlspecialchars($booking['check_out_date']); ?></td>
                    <td>
                        <a href="owner-dashboard.php?action=approve&booking_id=<?php echo $booking['booking_id']; ?>&room_id=<?php echo $booking['room_id']; ?>" class="btn btn-success" onclick="return confirm('ยืนยันการอนุมัติ?')">
                            <i class="fa-solid fa-check"></i>อนุมัติ
                        </a>
                        <a href="owner-dashboard.php?action=reject&booking_id=<?php echo $booking['booking_id']; ?>" class="btn btn-danger" onclick="return confirm('ยืนยันการปฏิเสธ?')">
                            <i class="fa-solid fa-times"></i>ปฏิเสธ
                        </a>
                    </td>
                </tr>
                <?php endwhile; ?>
            <?php else: ?>
                <tr><td colspan="5" style="text-align:center; padding: 20px;">ไม่มีคำขอจองที่รออนุมัติ</td></tr>
            <?php endif; ?>
            </tbody>
        </table>
    </div>
    <?php
    // === สิ้นสุดส่วนที่เพิ่ม ===

} elseif ($currentPage == 'booking_form') {
    $room_id = (int)$_GET['room_id'];
    $room = $connect->query("SELECT * FROM rooms WHERE id = $room_id")->fetch_assoc();
    ?>
    <div class="page-header"><h1>กรอกข้อมูลเพื่อส่งคำขอจอง</h1></div>
    <div class="form-container">
        <h3>ห้อง: <?php echo htmlspecialchars($room['name']); ?></h3><hr>
        <form action="owner-dashboard.php?action=create_booking" method="POST">
            <input type="hidden" name="room_id" value="<?php echo $room['id']; ?>">
            <div class="form-group"><label>ชื่อผู้เข้าพัก:</label><input type="text" name="customer_name" required></div>
            <div class="form-group"><label>เบอร์โทรศัพท์:</label><input type="text" name="customer_phone" required></div>
            <div class="form-group"><label>วันที่เข้าพัก:</label><input type="date" name="check_in_date" required></div>
            <div class="form-group"><label>วันที่ออก:</label><input type="date" name="check_out_date" required></div>
            <button type="submit" class="btn btn-primary">ส่งคำขอ</button>
        </form>
    </div>
    <?php

} elseif ($currentPage == 'return_room') {
    // ดึงข้อมูลห้องที่อนุมัติแล้ว และยังไม่คืน
    $active_bookings = $connect->query("
        SELECT b.id AS booking_id, r.id AS room_id, r.name AS room_name, b.customer_name
        FROM bookings b JOIN rooms r ON b.room_id = r.id
        WHERE b.booking_status = 'Approved'
    ");
    ?>
    <div class="page-header"><h1>รายการห้องพักที่ต้องคืน</h1></div>
    <div class="table-container">
        <table class="styled-table">
            <thead><tr><th>ห้อง</th><th>ผู้เข้าพัก</th><th>จัดการ</th></tr></thead>
            <tbody>
            <?php if($active_bookings->num_rows > 0): ?>
                <?php while($booking = $active_bookings->fetch_assoc()): ?>
                <tr>
                    <td><?php echo htmlspecialchars($booking['room_name']); ?></td>
                    <td><?php echo htmlspecialchars($booking['customer_name']); ?></td>
                    <td>
                        <a href="owner-dashboard.php?action=check_out&booking_id=<?php echo $booking['booking_id']; ?>&room_id=<?php echo $booking['room_id']; ?>" class="btn btn-info" onclick="return confirm('ยืนยันการคืนห้อง?')">คืนห้อง</a>
                    </td>
                </tr>
                <?php endwhile; ?>
            <?php else: ?>
                <tr><td colspan="3" style="text-align:center; padding: 20px;">ไม่มีห้องที่เข้าพักในขณะนี้</td></tr>
            <?php endif; ?>
            </tbody>
        </table>
    </div>
    <?php

} else { // หน้าเริ่มต้น หรือ booking
    $rooms = $connect->query("SELECT * FROM rooms ORDER BY id ASC");
    ?>
    <div class="page-header"><h1>จองห้องพัก (สำหรับลูกค้า Walk-in)</h1></div>
    <div class="table-container">
         <table class="styled-table">
            <thead><tr><th>ห้อง</th><th>ประเภท</th><th>สถานะ</th><th>ราคา</th><th>จัดการ</th></tr></thead>
            <tbody>
            <?php while($room = $rooms->fetch_assoc()): ?>
                <tr>
                    <td><?php echo htmlspecialchars($room['name']); ?></td>
                    <td><?php echo htmlspecialchars($room['type']); ?></td>
                    <td><?php echo htmlspecialchars($room['status']); ?></td>
                    <td><?php echo number_format($room['price']); ?></td>
                    <td>
                        <a href="owner-dashboard.php?page=booking_form&room_id=<?php echo $room['id']; ?>" class="btn btn-primary">ส่งคำขอจอง</a>
                    </td>
                </tr>
            <?php endwhile; ?>
            </tbody>
        </table>
    </div>
    <?php
}
?>
    </main>
</body>
</html>