<?php
session_start();
require('connect.php');

// ตรวจสอบการล็อกอิน
if (!isset($_SESSION['user_id'])) {
    header('Location: form-login.php');
    exit;
}

// ดึงข้อมูลสถิติจากฐานข้อมูล
$stats = [];

// 1. จำนวนการจองทั้งหมด
$result_total_booking = $connect->query("SELECT COUNT(*) AS total FROM bookings");
$stats['total_bookings'] = $result_total_booking->fetch_assoc()['total'] ?? 0;

// 2. รายได้รวม (เฉพาะที่อนุมัติแล้ว)
$result_total_income = $connect->query("SELECT SUM(total_price) AS income FROM bookings WHERE status = 'อนุมัติแล้ว'");
$stats['total_income'] = $result_total_income->fetch_assoc()['income'] ?? 0;

// 3. จำนวนลูกค้า (นับชื่อไม่ซ้ำ)
$result_total_customer = $connect->query("SELECT COUNT(DISTINCT customer_name) AS customer FROM bookings");
$stats['total_customers'] = $result_total_customer->fetch_assoc()['customer'] ?? 0;

// 4. จำนวนห้องพักทั้งหมด
$result_total_room = $connect->query("SELECT COUNT(*) AS room FROM rooms");
$stats['total_rooms'] = $result_total_room->fetch_assoc()['room'] ?? 0;

// 5. จำนวนห้องว่าง
$result_room_available = $connect->query("SELECT COUNT(*) AS available FROM rooms WHERE status = 'ว่าง'");
$stats['available_rooms'] = $result_room_available->fetch_assoc()['available'] ?? 0;

// 6. จำนวนห้องไม่ว่าง
$result_room_unavailable = $connect->query("SELECT COUNT(*) AS unavailable FROM rooms WHERE status = 'ไม่ว่าง'");
$stats['unavailable_rooms'] = $result_room_unavailable->fetch_assoc()['unavailable'] ?? 0;

// 7. การจองที่รออนุมัติ
$result_pending = $connect->query("SELECT COUNT(*) AS pending FROM bookings WHERE status = 'รออนุมัติ'");
$stats['pending_bookings'] = $result_pending->fetch_assoc()['pending'] ?? 0;

// 8. การจองที่อนุมัติแล้ว
$result_approved = $connect->query("SELECT COUNT(*) AS approved FROM bookings WHERE status = 'อนุมัติแล้ว'");
$stats['approved_bookings'] = $result_approved->fetch_assoc()['approved'] ?? 0;

// 9. การจองที่ยกเลิก
$result_cancelled = $connect->query("SELECT COUNT(*) AS cancelled FROM bookings WHERE status = 'ยกเลิก'");
$stats['cancelled_bookings'] = $result_cancelled->fetch_assoc()['cancelled'] ?? 0;

// 10. สถิติรายเดือน (6 เดือนล่าสุด)
$monthly_stats = [];
$sql_monthly = "SELECT 
    DATE_FORMAT(booking_date, '%Y-%m') as month,
    COUNT(*) as total_bookings,
    SUM(CASE WHEN status = 'อนุมัติแล้ว' THEN total_price ELSE 0 END) as income,
    COUNT(CASE WHEN status = 'อนุมัติแล้ว' THEN 1 END) as approved_count
FROM bookings 
WHERE booking_date >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
GROUP BY DATE_FORMAT(booking_date, '%Y-%m')
ORDER BY month DESC";
$result_monthly = $connect->query($sql_monthly);
while ($row = $result_monthly->fetch_assoc()) {
    $monthly_stats[] = $row;
}

// 11. สถิติตามประเภทห้อง
$room_type_stats = [];
$sql_room_type = "SELECT 
    r.type,
    COUNT(*) as total_rooms,
    COUNT(CASE WHEN r.status = 'ว่าง' THEN 1 END) as available_rooms,
    COUNT(CASE WHEN r.status = 'ไม่ว่าง' THEN 1 END) as occupied_rooms,
    AVG(r.price) as avg_price
FROM rooms r
GROUP BY r.type
ORDER BY total_rooms DESC";
$result_room_type = $connect->query($sql_room_type);
while ($row = $result_room_type->fetch_assoc()) {
    $room_type_stats[] = $row;
}

// 12. ลูกค้าที่จองบ่อยที่สุด (Top 10)
$top_customers = [];
$sql_top_customers = "SELECT 
    customer_name,
    customer_phone,
    COUNT(*) as booking_count,
    SUM(total_price) as total_spent
FROM bookings 
WHERE status = 'อนุมัติแล้ว'
GROUP BY customer_name, customer_phone
ORDER BY booking_count DESC, total_spent DESC
LIMIT 10";
$result_top_customers = $connect->query($sql_top_customers);
while ($row = $result_top_customers->fetch_assoc()) {
    $top_customers[] = $row;
}

// 13. ห้องที่ได้รับความนิยมที่สุด
$popular_rooms = [];
$sql_popular_rooms = "SELECT 
    r.name,
    r.type,
    r.price,
    COUNT(b.id) as booking_count,
    SUM(CASE WHEN b.status = 'อนุมัติแล้ว' THEN b.total_price ELSE 0 END) as total_income
FROM rooms r
LEFT JOIN bookings b ON r.id = b.room_id
GROUP BY r.id, r.name, r.type, r.price
ORDER BY booking_count DESC, total_income DESC
LIMIT 10";
$result_popular_rooms = $connect->query($sql_popular_rooms);
while ($row = $result_popular_rooms->fetch_assoc()) {
    $popular_rooms[] = $row;
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>รายงานสถิติ - ระบบจัดการรีสอร์ท</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Sarabun:wght@400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary-color: #7b61ff;
            --primary-light: #f0edff;
            --primary-dark: #5c48d3;
            --secondary-color: #f8f9fa;
            --border-color-light: #e9ecef;
            --text-color: #333;
            --text-secondary: #6c757d;
            --bg-color: #f7f7fc;
            --green-color: #28a745;
            --red-color: #dc3545;
            --orange-color: #fd7e14;
            --blue-color: #17a2b8;
            --purple-color: #6f42c1;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Sarabun', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
            padding: 30px;
            border-radius: 20px;
            margin-bottom: 30px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            text-align: center;
        }

        .header h1 {
            color: var(--text-color);
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .header p {
            color: var(--text-secondary);
            font-size: 16px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 1px solid rgba(123,97,255,0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--purple-color));
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(123,97,255,0.2);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28px;
            margin-bottom: 20px;
            background: linear-gradient(135deg, var(--primary-light) 0%, rgba(123,97,255,0.1) 100%);
            color: var(--primary-color);
        }

        .stat-icon.booking { background: linear-gradient(135deg, rgba(123,97,255,0.1) 0%, rgba(123,97,255,0.05) 100%); color: var(--primary-color); }
        .stat-icon.income { background: linear-gradient(135deg, rgba(40,167,69,0.1) 0%, rgba(40,167,69,0.05) 100%); color: var(--green-color); }
        .stat-icon.customer { background: linear-gradient(135deg, rgba(253,126,20,0.1) 0%, rgba(253,126,20,0.05) 100%); color: var(--orange-color); }
        .stat-icon.room { background: linear-gradient(135deg, rgba(23,162,184,0.1) 0%, rgba(23,162,184,0.05) 100%); color: var(--blue-color); }
        .stat-icon.available { background: linear-gradient(135deg, rgba(40,167,69,0.1) 0%, rgba(40,167,69,0.05) 100%); color: var(--green-color); }
        .stat-icon.occupied { background: linear-gradient(135deg, rgba(220,53,69,0.1) 0%, rgba(220,53,69,0.05) 100%); color: var(--red-color); }
        .stat-icon.pending { background: linear-gradient(135deg, rgba(253,126,20,0.1) 0%, rgba(253,126,20,0.05) 100%); color: var(--orange-color); }
        .stat-icon.approved { background: linear-gradient(135deg, rgba(40,167,69,0.1) 0%, rgba(40,167,69,0.05) 100%); color: var(--green-color); }
        .stat-icon.cancelled { background: linear-gradient(135deg, rgba(220,53,69,0.1) 0%, rgba(220,53,69,0.05) 100%); color: var(--red-color); }

        .stat-number {
            font-size: 36px;
            font-weight: 700;
            color: var(--text-color);
            margin-bottom: 8px;
        }

        .stat-label {
            font-size: 16px;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .chart-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }

        .chart-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 1px solid rgba(123,97,255,0.1);
        }

        .chart-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 20px;
            text-align: center;
        }

        .table-section {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 1px solid rgba(123,97,255,0.1);
            margin-bottom: 30px;
        }

        .table-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 20px;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .data-table th,
        .data-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid var(--border-color-light);
        }

        .data-table th {
            background-color: var(--primary-light);
            color: var(--primary-dark);
            font-weight: 600;
            font-size: 14px;
        }

        .data-table tr:hover {
            background-color: rgba(123,97,255,0.05);
        }

        .btn-back {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            padding: 15px 25px;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            margin-bottom: 30px;
        }

        .btn-back:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(123,97,255,0.3);
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            color: white;
            text-transform: uppercase;
        }

        .status-approved { background: linear-gradient(135deg, var(--green-color) 0%, #20893a 100%); }
        .status-pending { background: linear-gradient(135deg, var(--orange-color) 0%, #e65a00 100%); }
        .status-cancelled { background: linear-gradient(135deg, var(--red-color) 0%, #b82c3a 100%); }

        @media (max-width: 768px) {
            .chart-section {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="owner.php" class="btn-back">
            <i class="fa-solid fa-arrow-left"></i>
            กลับไปหน้า Dashboard
        </a>

        <div class="header">
            <h1><i class="fa-solid fa-chart-line"></i> รายงานสถิติการเข้าใช้จองรีสอร์ท</h1>
            <p>ข้อมูลสถิติการใช้งานระบบจัดการรีสอร์ท</p>
        </div>

        <!-- สถิติหลัก -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon booking">
                    <i class="fa-solid fa-calendar-check"></i>
                </div>
                <div class="stat-number"><?php echo number_format($stats['total_bookings']); ?></div>
                <div class="stat-label">การจองทั้งหมด</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon income">
                    <i class="fa-solid fa-money-bill-wave"></i>
                </div>
                <div class="stat-number">฿<?php echo number_format($stats['total_income'], 2); ?></div>
                <div class="stat-label">รายได้รวม</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon customer">
                    <i class="fa-solid fa-users"></i>
                </div>
                <div class="stat-number"><?php echo number_format($stats['total_customers']); ?></div>
                <div class="stat-label">ลูกค้าทั้งหมด</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon room">
                    <i class="fa-solid fa-bed"></i>
                </div>
                <div class="stat-number"><?php echo number_format($stats['total_rooms']); ?></div>
                <div class="stat-label">ห้องพักทั้งหมด</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon available">
                    <i class="fa-solid fa-check-circle"></i>
                </div>
                <div class="stat-number"><?php echo number_format($stats['available_rooms']); ?></div>
                <div class="stat-label">ห้องว่าง</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon occupied">
                    <i class="fa-solid fa-times-circle"></i>
                </div>
                <div class="stat-number"><?php echo number_format($stats['unavailable_rooms']); ?></div>
                <div class="stat-label">ห้องไม่ว่าง</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon pending">
                    <i class="fa-solid fa-clock"></i>
                </div>
                <div class="stat-number"><?php echo number_format($stats['pending_bookings']); ?></div>
                <div class="stat-label">รออนุมัติ</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon approved">
                    <i class="fa-solid fa-check"></i>
                </div>
                <div class="stat-number"><?php echo number_format($stats['approved_bookings']); ?></div>
                <div class="stat-label">อนุมัติแล้ว</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon cancelled">
                    <i class="fa-solid fa-ban"></i>
                </div>
                <div class="stat-number"><?php echo number_format($stats['cancelled_bookings']); ?></div>
                <div class="stat-label">ยกเลิก</div>
            </div>
        </div>

        <!-- กราฟ -->
        <div class="chart-section">
            <div class="chart-card">
                <div class="chart-title">สถิติรายเดือน (6 เดือนล่าสุด)</div>
                <canvas id="monthlyChart"></canvas>
            </div>

            <div class="chart-card">
                <div class="chart-title">สถิติตามประเภทห้อง</div>
                <canvas id="roomTypeChart"></canvas>
            </div>
        </div>

        <!-- ตารางข้อมูล -->
        <div class="table-section">
            <div class="table-title">ลูกค้าที่จองบ่อยที่สุด (Top 10)</div>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>ลำดับ</th>
                        <th>ชื่อลูกค้า</th>
                        <th>เบอร์โทร</th>
                        <th>จำนวนการจอง</th>
                        <th>ยอดรวมที่ใช้จ่าย</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($top_customers as $index => $customer): ?>
                    <tr>
                        <td><?php echo $index + 1; ?></td>
                        <td><?php echo htmlspecialchars($customer['customer_name']); ?></td>
                        <td><?php echo htmlspecialchars($customer['customer_phone']); ?></td>
                        <td><?php echo number_format($customer['booking_count']); ?></td>
                        <td>฿<?php echo number_format($customer['total_spent'], 2); ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <div class="table-section">
            <div class="table-title">ห้องที่ได้รับความนิยมที่สุด (Top 10)</div>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>ลำดับ</th>
                        <th>ชื่อห้อง</th>
                        <th>ประเภท</th>
                        <th>ราคา</th>
                        <th>จำนวนการจอง</th>
                        <th>รายได้รวม</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($popular_rooms as $index => $room): ?>
                    <tr>
                        <td><?php echo $index + 1; ?></td>
                        <td><?php echo htmlspecialchars($room['name']); ?></td>
                        <td><?php echo htmlspecialchars($room['type']); ?></td>
                        <td>฿<?php echo number_format($room['price']); ?></td>
                        <td><?php echo number_format($room['booking_count']); ?></td>
                        <td>฿<?php echo number_format($room['total_income'], 2); ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // กราฟรายเดือน
        const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
        const monthlyData = <?php echo json_encode($monthly_stats); ?>;
        
        new Chart(monthlyCtx, {
            type: 'line',
            data: {
                labels: monthlyData.map(item => {
                    const date = new Date(item.month + '-01');
                    return date.toLocaleDateString('th-TH', { year: 'numeric', month: 'long' });
                }).reverse(),
                datasets: [{
                    label: 'จำนวนการจอง',
                    data: monthlyData.map(item => item.total_bookings).reverse(),
                    borderColor: '#7b61ff',
                    backgroundColor: 'rgba(123, 97, 255, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: 'รายได้ (พันบาท)',
                    data: monthlyData.map(item => item.income / 1000).reverse(),
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    tension: 0.4,
                    fill: true,
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'จำนวนการจอง'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'รายได้ (พันบาท)'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                }
            }
        });

        // กราฟประเภทห้อง
        const roomTypeCtx = document.getElementById('roomTypeChart').getContext('2d');
        const roomTypeData = <?php echo json_encode($room_type_stats); ?>;
        
        new Chart(roomTypeCtx, {
            type: 'doughnut',
            data: {
                labels: roomTypeData.map(item => item.type),
                datasets: [{
                    data: roomTypeData.map(item => item.total_rooms),
                    backgroundColor: [
                        '#7b61ff',
                        '#28a745',
                        '#fd7e14',
                        '#17a2b8',
                        '#6f42c1',
                        '#e83e8c'
                    ],
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.parsed;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return `${label}: ${value} ห้อง (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });

        // Auto-refresh ข้อมูลทุก 5 นาที
        setInterval(function() {
            location.reload();
        }, 300000);
    </script>
</body>
</html> 