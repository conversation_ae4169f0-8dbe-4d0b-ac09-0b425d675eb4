<?php
session_start();
require('connect.php');

// ตรวจสอบสิทธิ์การเข้าใช้งาน (employee เท่านั้น)
if (!isset($_SESSION['id_account']) || !isset($_SESSION['role_account']) || $_SESSION['role_account'] !== 'employee') {
    die(header('Location: form-login.php'));
}

$id_account = $_SESSION['id_account'];
$query_show = "SELECT username_account, role_account, images_account FROM account WHERE id_account = '$id_account'";
$call_back_show = mysqli_query($connect, $query_show);
$result_show = mysqli_fetch_assoc($call_back_show);

// สำหรับ active menu
$current_page_file = basename($_SERVER['SCRIPT_NAME']);
switch ($current_page_file) {
    case 'employee.php':
        $currentPage = 'dashboard';
        break;
    case 'room-management.php':
        $currentPage = 'manage_room';
        break;
    case 'booking-management.php':
        $currentPage = 'approve_booking';
        break;
    case 'return-the-room.php':
        $currentPage = 'return_room';
        break;
    case 'booking-history.php':
        $currentPage = 'booking_history';
        break;
    case 'beverage-management.php':
        $currentPage = 'beverage';
        break;
    case 'receipt.php':
        $currentPage = 'receipt';
        break;
    case 'change-password.php':
        $currentPage = 'change_password';
        break;
    default:
        $currentPage = '';
}

// จัดการการคืนห้อง
if (isset($_POST['action']) && $_POST['action'] === 'return_room') {
    $room_id = (int)$_POST['room_id'];
    $booking_id = (int)$_POST['booking_id'];
    $return_reason = mysqli_real_escape_string($connect, $_POST['return_reason']);
    
    // อัปเดตสถานะห้องเป็น available
    $update_room = "UPDATE room SET status = 'available' WHERE id_room = ?";
    $stmt_room = $connect->prepare($update_room);
    $stmt_room->bind_param('i', $room_id);
    
    if ($stmt_room->execute()) {
        // อัปเดตสถานะการจองเป็น "คืนห้องแล้ว"
        $update_booking = "UPDATE bookings SET status = 'คืนห้องแล้ว', return_reason = ?, returned_at = NOW() WHERE id = ?";
        $stmt_booking = $connect->prepare($update_booking);
        $stmt_booking->bind_param('si', $return_reason, $booking_id);
        
        if ($stmt_booking->execute()) {
            $_SESSION['message'] = 'คืนห้องเรียบร้อยแล้ว';
            $_SESSION['message_type'] = 'success';
            header('Location: return-the-room.php');
            exit;
        } else {
            $_SESSION['message'] = 'เกิดข้อผิดพลาดในการอัปเดตการจอง';
            $_SESSION['message_type'] = 'error';
            header('Location: return-the-room.php');
            exit;
        }
    } else {
        $_SESSION['message'] = 'เกิดข้อผิดพลาดในการคืนห้อง';
        $_SESSION['message_type'] = 'error';
        header('Location: return-the-room.php');
        exit;
    }
}

// ดึงข้อมูลห้องที่ถูกจองและใช้งานอยู่
$occupied_rooms = [];

// Debug: ตรวจสอบข้อมูลในตาราง
$debug_query = "SELECT COUNT(*) as total_rooms FROM room";
$debug_result = mysqli_query($connect, $debug_query);
$debug_data = mysqli_fetch_assoc($debug_result);
echo "<!-- Debug: จำนวนห้องทั้งหมด: " . $debug_data['total_rooms'] . " -->";

$debug_query2 = "SELECT COUNT(*) as total_bookings FROM bookings";
$debug_result2 = mysqli_query($connect, $debug_query2);
$debug_data2 = mysqli_fetch_assoc($debug_result2);
echo "<!-- Debug: จำนวนการจองทั้งหมด: " . $debug_data2['total_bookings'] . " -->";

// แก้ไข query ให้ดึงข้อมูลได้ถูกต้อง
$query = "SELECT r.*, b.id as booking_id, b.customer_name, b.check_in_date, b.check_out_date, b.created_at as booking_date
          FROM room r 
          JOIN bookings b ON r.id_room = b.room_id 
          WHERE b.status = 'อนุมัติแล้ว'
          ORDER BY b.created_at ASC";

$result = mysqli_query($connect, $query);
if (!$result) {
    echo "<!-- Debug: SQL Error: " . mysqli_error($connect) . " -->";
}

while ($row = mysqli_fetch_assoc($result)) {
    $occupied_rooms[] = $row;
}

echo "<!-- Debug: จำนวนห้องที่ถูกจอง: " . count($occupied_rooms) . " -->";
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>คืนห้องพัก - Employee</title>
    
    <!-- Font Awesome CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }
    
    body {
        font-family: 'Kanit', sans-serif;
        background-color: #f8f9fa;
    }
    
    :root {
        --primary-color: #7b43ff;
        --light-primary-color: #f3f0ff;
        --text-color: #495057;
        --text-secondary-color: #6c757d;
        --sidebar-bg: #ffffff;
        --border-color: #e7e7e7;
        --header-bg: #f8f9fa;
        --hover-bg: #f8f9fa;
        --success-color: #28a745;
        --danger-color: #dc3545;
        --warning-color: #ffc107;
        --info-color: #17a2b8;
    }
    
    /* Sidebar Styles */
    .sidebar {
        width: 280px;
        height: 100vh;
        background: var(--sidebar-bg);
        border-right: 1px solid var(--border-color);
        position: fixed;
        left: 0;
        top: 0;
        overflow-y: auto;
        box-shadow: 2px 0 10px rgba(0,0,0,0.1);
    }
    
    .sidebar-header {
        padding: 20px;
        border-bottom: 1px solid var(--border-color);
    }
    
    .logo {
        display: flex;
        align-items: center;
        text-decoration: none;
        color: var(--primary-color);
        font-size: 18px;
        font-weight: 700;
    }
    
    .logo i {
        margin-right: 10px;
        font-size: 24px;
    }
    
    .user-profile {
        padding: 20px;
        border-bottom: 1px solid var(--border-color);
        display: flex;
        align-items: center;
    }
    
    .user-avatar {
        width: 50px;
        height: 50px;
        background: var(--light-primary-color);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
    }
    
    .user-avatar i {
        color: var(--primary-color);
        font-size: 20px;
    }
    
    .user-info h5 {
        color: var(--text-color);
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 5px;
    }
    
    .user-info p {
        color: var(--text-secondary-color);
        font-size: 14px;
        margin: 0;
    }
    
    .nav-header {
        padding: 20px 20px 10px;
        font-size: 12px;
        font-weight: 700;
        color: #adb5bd;
        text-transform: uppercase;
        letter-spacing: 1px;
    }
    
    .sidebar-nav li a {
        display: flex;
        align-items: center;
        padding: 15px 20px;
        margin: 2px 10px;
        color: var(--text-color);
        text-decoration: none;
        font-size: 15px;
        font-weight: 500;
        border-radius: 10px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }
    
    .sidebar-nav li a::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        width: 0;
        background: linear-gradient(90deg, var(--primary-color), #9c6fff);
        transition: width 0.3s ease;
        z-index: -1;
    }
    
    .sidebar-nav li a:hover::before {
        width: 100%;
    }
    
    .sidebar-nav li a i {
        width: 24px;
        margin-right: 15px;
        text-align: center;
        font-size: 18px;
        color: var(--text-secondary-color);
        transition: all 0.3s ease;
    }
    
    .sidebar-nav li.active a {
        background: linear-gradient(135deg, var(--primary-color), #9c6fff);
        color: #fff;
        font-weight: 600;
        box-shadow: 0 4px 15px rgba(123, 67, 255, 0.3);
        transform: translateX(5px);
    }
    
    .sidebar-nav li.active a i {
        color: #fff;
        transform: scale(1.1);
    }
    
    .sidebar-nav li a:hover {
        color: #fff;
        transform: translateX(5px);
    }
    
    .sidebar-nav li a:hover i {
        color: #fff;
        transform: scale(1.1);
    }
    
    .logout-item {
        margin-top: 20px;
        border-top: 1px solid var(--border-color);
        padding-top: 20px;
    }
    
    .logout-item a {
        color: var(--danger-color) !important;
    }
    
    .logout-item a:hover {
        background: var(--danger-color) !important;
        color: #fff !important;
    }
    
    /* Main Content */
    .main-content {
        margin-left: 280px;
        padding: 30px;
        min-height: 100vh;
    }
    
    .content-header {
        margin-bottom: 30px;
    }
    
    .content-header h1 {
        color: var(--text-color);
        font-size: 28px;
        font-weight: 700;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
    }
    
    .content-header h1 i {
        margin-right: 15px;
        color: var(--primary-color);
    }
    
    .content-header p {
        color: var(--text-secondary-color);
        font-size: 16px;
        margin: 0;
    }
    
    /* Alert Styles */
    .alert {
        padding: 15px 20px;
        border-radius: 10px;
        margin-bottom: 20px;
        font-weight: 500;
    }
    
    .alert-success {
        background: rgba(40, 167, 69, 0.1);
        color: var(--success-color);
        border: 1px solid rgba(40, 167, 69, 0.2);
    }
    
    .alert-error {
        background: rgba(220, 53, 69, 0.1);
        color: var(--danger-color);
        border: 1px solid rgba(220, 53, 69, 0.2);
    }
    
    /* Room Cards */
    .room-container {
        background: #fff;
        border-radius: 15px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        overflow: hidden;
    }
    
    .room-header {
        padding: 25px 30px;
        border-bottom: 1px solid var(--border-color);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .room-header h2 {
        color: var(--text-color);
        font-size: 20px;
        font-weight: 600;
        display: flex;
        align-items: center;
    }
    
    .room-header h2 i {
        margin-right: 10px;
        color: var(--primary-color);
    }
    
    .room-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 20px;
        padding: 30px;
    }
    
    .room-card {
        background: #fff;
        border: 1px solid var(--border-color);
        border-radius: 15px;
        overflow: hidden;
        transition: all 0.3s ease;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    }
    
    .room-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }
    
    .room-image {
        height: 200px;
        background: linear-gradient(135deg, var(--primary-color), #9c6fff);
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        font-size: 48px;
    }
    
    .room-info {
        padding: 20px;
    }
    
    .room-number {
        font-size: 18px;
        font-weight: 700;
        color: var(--text-color);
        margin-bottom: 10px;
    }
    
    .room-type {
        color: var(--text-secondary-color);
        font-size: 14px;
        margin-bottom: 15px;
    }
    
    .booking-info {
        background: var(--light-primary-color);
        padding: 15px;
        border-radius: 10px;
        margin-bottom: 15px;
    }
    
    .booking-info h4 {
        color: var(--primary-color);
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 10px;
    }
    
    .info-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        font-size: 13px;
    }
    
    .info-row:last-child {
        margin-bottom: 0;
    }
    
    .info-label {
        color: var(--text-secondary-color);
        font-weight: 500;
    }
    
    .info-value {
        color: var(--text-color);
        font-weight: 600;
    }
    
    .return-btn {
        width: 100%;
        padding: 12px;
        background: var(--danger-color);
        color: #fff;
        border: none;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .return-btn:hover {
        background: #c82333;
        transform: translateY(-2px);
    }
    
    .return-btn i {
        margin-right: 8px;
    }
    
    /* Modal Styles */
    .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        display: none;
        align-items: center;
        justify-content: center;
        z-index: 1000;
    }
    
    .modal-content {
        background: #fff;
        border-radius: 15px;
        padding: 30px;
        width: 90%;
        max-width: 500px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    }
    
    .modal-header {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
    }
    
    .modal-header i {
        font-size: 24px;
        color: var(--danger-color);
        margin-right: 15px;
    }
    
    .modal-header h3 {
        color: var(--text-color);
        font-size: 20px;
        font-weight: 600;
        margin: 0;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-group label {
        display: block;
        color: var(--text-color);
        font-weight: 600;
        margin-bottom: 8px;
    }
    
    .form-group textarea {
        width: 100%;
        padding: 12px;
        border: 1px solid var(--border-color);
        border-radius: 8px;
        font-family: inherit;
        font-size: 14px;
        resize: vertical;
        min-height: 100px;
    }
    
    .form-group textarea:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(123, 67, 255, 0.1);
    }
    
    .modal-buttons {
        display: flex;
        gap: 10px;
        justify-content: flex-end;
        margin-top: 25px;
    }
    
    .btn {
        padding: 10px 20px;
        border: none;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .btn-secondary {
        background: var(--text-secondary-color);
        color: #fff;
    }
    
    .btn-secondary:hover {
        background: #5a6268;
    }
    
    .btn-danger {
        background: var(--danger-color);
        color: #fff;
    }
    
    .btn-danger:hover {
        background: #c82333;
    }
    
    /* Empty State */
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: var(--text-secondary-color);
    }
    
    .empty-state i {
        font-size: 64px;
        margin-bottom: 20px;
        color: var(--border-color);
    }
    
    .empty-state h3 {
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 10px;
        color: var(--text-color);
    }
    
    .empty-state p {
        font-size: 16px;
        margin: 0;
    }
    </style>
</head>
<body>
    
    <aside class="sidebar">
    <div class="sidebar-header">
        <a href="employee.php" class="logo">
            <i class="fa-solid fa-hotel"></i>
            <span>ระบบจัดการรีสอร์ท</span>
        </a>
    </div>
    <div class="user-profile">
        <div class="user-avatar">
            <i class="fa-solid fa-user"></i>
        </div>
        <div class="user-info">
            <h5><?php echo htmlspecialchars($result_show['username_account']); ?></h5>
            <p><?php echo htmlspecialchars($result_show['role_account']); ?></p>
        </div>
    </div>
    <div class="sidebar-content">
        <ul class="sidebar-nav">
            <li class="nav-header">
                <i class="fa-solid fa-bed"></i>
                <span>ห้องพัก</span>
            </li>
            <li class="<?php if ($currentPage == 'dashboard') echo 'active'; ?>">
                <a href="employee.php"><i class="fa-solid fa-tachometer-alt"></i><span>จัดการห้องพัก</span></a>
            </li>
            
            <li class="<?php if ($currentPage == 'approve_booking') echo 'active'; ?>">
                <a href="booking-management.php"><i class="fa-solid fa-calendar-check"></i><span>อนุมัติการจอง</span></a>
            </li>
            <li class="<?php if ($currentPage == 'return_room') echo 'active'; ?>">
                <a href="return-the-room.php"><i class="fa-solid fa-key"></i><span>คืนห้องพัก</span></a>
            </li>
            <li class="<?php if ($currentPage == 'booking_history') echo 'active'; ?>">
                <a href="booking-history.php"><i class="fa-solid fa-history"></i><span>ประวัติการจอง</span></a>
            </li>
            
            <li class="nav-header">
                <i class="fa-solid fa-concierge-bell"></i>
                <span>บริการเสริม</span>
            </li>
            <li class="<?php if ($currentPage == 'beverage') echo 'active'; ?>">
                <a href="beverage-management.php"><i class="fa-solid fa-cocktail"></i><span>จัดการเครื่องดื่ม</span></a>
            </li>
            <li class="<?php if ($currentPage == 'bakery') echo 'active'; ?>">
                <a href="bakery-management.php"><i class="fa-solid fa-cookie-bite"></i><span>จัดขนมเบเกอรี่</span></a>
            </li>
            
            
            <li class="nav-header">
                <i class="fa-solid fa-cash-register"></i>
                <span>การเงิน</span>
            </li>
            <li class="<?php if ($currentPage == 'receipt') echo 'active'; ?>">
                <a href="receipt.php"><i class="fa-solid fa-file-invoice-dollar"></i><span>ออกใบเสร็จ</span></a>
            </li>
            
            <li class="nav-header">
                <i class="fa-solid fa-user-cog"></i>
                <span>บัญชีผู้ใช้</span>
            </li>
            <li class="<?php if ($currentPage == 'change_password') echo 'active'; ?>">
                <a href="change-password.php"><i class="fa-solid fa-lock"></i><span>เปลี่ยนรหัสผ่าน</span></a>
            </li>
            
            <li class="logout-item">
                <a href="form-login.php?logout=1"><i class="fa-solid fa-sign-out-alt"></i><span>ออกจากระบบ</span></a>
            </li>
        </ul>
    </div>
</aside>

    <!-- Main Content -->
    <div class="main-content">
        
        <?php
        // แสดงข้อความแจ้งเตือน
        if (isset($_SESSION['message'])) {
            $message = $_SESSION['message'];
            $message_type = $_SESSION['message_type'] ?? 'success';
            echo "<div class='alert alert-{$message_type}'>{$message}</div>";
            unset($_SESSION['message']);
            unset($_SESSION['message_type']);
        }
        ?>

        <div class="content-header">
            <h1><i class="fa-solid fa-key"></i> คืนห้องพัก</h1>
            <p>จัดการการคืนห้องพักเมื่อลูกค้าออกกระทันหัน</p>
        </div>

        <div class="room-container">
            <div class="room-header">
                <h2><i class="fa-solid fa-bed"></i> ห้องที่ถูกจองและใช้งานอยู่</h2>
            </div>
            
            <?php if (empty($occupied_rooms)): ?>
                <div class="empty-state">
                    <i class="fa-solid fa-bed"></i>
                    <h3>ไม่มีห้องที่ถูกจอง</h3>
                    <p>ยังไม่มีห้องที่ถูกจองและใช้งานอยู่</p>
                </div>
            <?php else: ?>
                <div class="room-grid">
                    <?php foreach ($occupied_rooms as $room): ?>
                        <div class="room-card">
                            <div class="room-image">
                                <i class="fa-solid fa-bed"></i>
                            </div>
                            <div class="room-info">
                                <div class="room-number">ห้อง <?php echo htmlspecialchars($room['room_number']); ?></div>
                                <div class="room-type"><?php echo htmlspecialchars($room['room_type']); ?></div>
                                
                                <div class="booking-info">
                                    <h4>ข้อมูลการจอง</h4>
                                    <div class="info-row">
                                        <span class="info-label">ลูกค้า:</span>
                                        <span class="info-value"><?php echo htmlspecialchars($room['customer_name']); ?></span>
                                    </div>
                                    <div class="info-row">
                                        <span class="info-label">วันที่เช็คอิน:</span>
                                        <span class="info-value"><?php echo date('d/m/Y', strtotime($room['check_in_date'])); ?></span>
                                    </div>
                                    <div class="info-row">
                                        <span class="info-label">วันที่เช็คเอาท์:</span>
                                        <span class="info-value"><?php echo date('d/m/Y', strtotime($room['check_out_date'])); ?></span>
                                    </div>
                                    <div class="info-row">
                                        <span class="info-label">วันที่จอง:</span>
                                        <span class="info-value"><?php echo date('d/m/Y H:i', strtotime($room['booking_date'])); ?></span>
                                    </div>
                                </div>
                                
                                <button class="return-btn" onclick="showReturnModal(<?php echo $room['id_room']; ?>, <?php echo $room['booking_id']; ?>, '<?php echo htmlspecialchars($room['room_number']); ?>')">
                                    <i class="fa-solid fa-key"></i>
                                    คืนห้อง
                                </button>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Return Room Modal -->
    <div class="modal-overlay" id="returnModal">
        <div class="modal-content">
            <div class="modal-header">
                <i class="fa-solid fa-key"></i>
                <h3>คืนห้องพัก</h3>
            </div>
            <form method="POST" action="">
                <input type="hidden" name="action" value="return_room">
                <input type="hidden" name="room_id" id="modalRoomId">
                <input type="hidden" name="booking_id" id="modalBookingId">
                
                <div class="form-group">
                    <label for="return_reason">เหตุผลการคืนห้อง:</label>
                    <textarea name="return_reason" id="return_reason" placeholder="ระบุเหตุผลการคืนห้อง เช่น ลูกค้าออกกระทันหัน, มีเหตุฉุกเฉิน, ฯลฯ" required></textarea>
                </div>
                
                <div class="modal-buttons">
                    <button type="button" class="btn btn-secondary" onclick="hideReturnModal()">ยกเลิก</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fa-solid fa-key"></i>
                        ยืนยันการคืนห้อง
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function showReturnModal(roomId, bookingId, roomNumber) {
            document.getElementById('modalRoomId').value = roomId;
            document.getElementById('modalBookingId').value = bookingId;
            document.getElementById('returnModal').style.display = 'flex';
        }
        
        function hideReturnModal() {
            document.getElementById('returnModal').style.display = 'none';
            document.getElementById('return_reason').value = '';
        }
        
        // ปิด modal เมื่อคลิกพื้นหลัง
        document.getElementById('returnModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideReturnModal();
            }
        });
    </script>
</body>
</html>
