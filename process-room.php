<?php
session_start();
require('connect.php');

// ตรวจสอบสิทธิ์การเข้าใช้งาน (employee เท่านั้น)
if (!isset($_SESSION['id_account']) || !isset($_SESSION['role_account']) || $_SESSION['role_account'] !== 'employee') {
    die(header('Location: form-login.php'));
}

$action = $_POST['action'] ?? '';

switch ($action) {
    case 'add':
        addRoom();
        break;
    case 'edit':
        editRoom();
        break;
    case 'delete':
        deleteRoom();
        break;
    default:
        $_SESSION['message'] = 'การดำเนินการไม่ถูกต้อง';
        $_SESSION['message_type'] = 'danger';
        header('Location: room-management.php');
        exit();
}

function addRoom() {
    global $connect;
    
    // รับข้อมูลจากฟอร์ม
    $room_number = mysqli_real_escape_string($connect, $_POST['room_number']);
    $room_type = mysqli_real_escape_string($connect, $_POST['room_type']);
    $price = floatval($_POST['price']);
    $status = mysqli_real_escape_string($connect, $_POST['status']);
    $description = mysqli_real_escape_string($connect, $_POST['description']);
    
    // ตรวจสอบว่าหมายเลขห้องซ้ำหรือไม่
    $check_query = "SELECT id_room FROM room WHERE room_number = '$room_number'";
    $check_result = mysqli_query($connect, $check_query);
    
    if (mysqli_num_rows($check_result) > 0) {
        $_SESSION['message'] = 'หมายเลขห้องนี้มีอยู่ในระบบแล้ว';
        $_SESSION['message_type'] = 'danger';
        header('Location: room-management.php');
        exit();
    }
    
    // จัดการอัปโหลดรูปภาพ
    $image_path = '';
    if (isset($_FILES['room_image']) && $_FILES['room_image']['error'] == 0) {
        $upload_dir = 'uploads/';
        $file_extension = pathinfo($_FILES['room_image']['name'], PATHINFO_EXTENSION);
        $file_name = 'room_' . time() . '_' . uniqid() . '.' . $file_extension;
        $upload_path = $upload_dir . $file_name;
        
        if (move_uploaded_file($_FILES['room_image']['tmp_name'], $upload_path)) {
            $image_path = $upload_path;
        }
    }
    
    // เพิ่มข้อมูลห้องพัก
    $query = "INSERT INTO room (room_number, room_type, price, status, description, image) 
              VALUES ('$room_number', '$room_type', $price, '$status', '$description', '$image_path')";
    
    if (mysqli_query($connect, $query)) {
        $_SESSION['message'] = 'เพิ่มห้องพักสำเร็จ';
        $_SESSION['message_type'] = 'success';
    } else {
        $_SESSION['message'] = 'เกิดข้อผิดพลาดในการเพิ่มห้องพัก: ' . mysqli_error($connect);
        $_SESSION['message_type'] = 'danger';
    }
    
    header('Location: room-management.php');
    exit();
}

function editRoom() {
    global $connect;
    
    $room_id = intval($_POST['room_id']);
    $room_number = mysqli_real_escape_string($connect, $_POST['room_number']);
    $room_type = mysqli_real_escape_string($connect, $_POST['room_type']);
    $price = floatval($_POST['price']);
    $status = mysqli_real_escape_string($connect, $_POST['status']);
    $description = mysqli_real_escape_string($connect, $_POST['description']);
    
    // ตรวจสอบว่าหมายเลขห้องซ้ำหรือไม่ (ยกเว้นห้องที่กำลังแก้ไข)
    $check_query = "SELECT id_room FROM room WHERE room_number = '$room_number' AND id_room != $room_id";
    $check_result = mysqli_query($connect, $check_query);
    
    if (mysqli_num_rows($check_result) > 0) {
        $_SESSION['message'] = 'หมายเลขห้องนี้มีอยู่ในระบบแล้ว';
        $_SESSION['message_type'] = 'danger';
        header('Location: room-management.php');
        exit();
    }
    
    // จัดการอัปโหลดรูปภาพใหม่
    $image_path = '';
    if (isset($_FILES['room_image']) && $_FILES['room_image']['error'] == 0) {
        $upload_dir = 'uploads/';
        $file_extension = pathinfo($_FILES['room_image']['name'], PATHINFO_EXTENSION);
        $file_name = 'room_' . time() . '_' . uniqid() . '.' . $file_extension;
        $upload_path = $upload_dir . $file_name;
        
        if (move_uploaded_file($_FILES['room_image']['tmp_name'], $upload_path)) {
            $image_path = $upload_path;
            
            // ลบรูปภาพเก่า
            $old_image_query = "SELECT image FROM room WHERE id_room = $room_id";
            $old_image_result = mysqli_query($connect, $old_image_query);
            if ($old_image_row = mysqli_fetch_assoc($old_image_result)) {
                if (!empty($old_image_row['image']) && file_exists($old_image_row['image'])) {
                    unlink($old_image_row['image']);
                }
            }
        }
    }
    
    // อัปเดตข้อมูลห้องพัก
    if (!empty($image_path)) {
        $query = "UPDATE room SET room_number = '$room_number', room_type = '$room_type', 
                  price = $price, status = '$status', description = '$description', image = '$image_path' 
                  WHERE id_room = $room_id";
    } else {
        $query = "UPDATE room SET room_number = '$room_number', room_type = '$room_type', 
                  price = $price, status = '$status', description = '$description' 
                  WHERE id_room = $room_id";
    }
    
    if (mysqli_query($connect, $query)) {
        $_SESSION['message'] = 'แก้ไขห้องพักสำเร็จ';
        $_SESSION['message_type'] = 'success';
    } else {
        $_SESSION['message'] = 'เกิดข้อผิดพลาดในการแก้ไขห้องพัก: ' . mysqli_error($connect);
        $_SESSION['message_type'] = 'danger';
    }
    
    header('Location: room-management.php');
    exit();
}

function deleteRoom() {
    global $connect;
    
    $room_id = intval($_POST['room_id']);
    
    // ลบรูปภาพ
    $image_query = "SELECT image FROM room WHERE id_room = $room_id";
    $image_result = mysqli_query($connect, $image_query);
    if ($image_row = mysqli_fetch_assoc($image_result)) {
        if (!empty($image_row['image']) && file_exists($image_row['image'])) {
            unlink($image_row['image']);
        }
    }
    
    // ลบข้อมูลห้องพัก
    $query = "DELETE FROM room WHERE id_room = $room_id";
    
    if (mysqli_query($connect, $query)) {
        $_SESSION['message'] = 'ลบห้องพักสำเร็จ';
        $_SESSION['message_type'] = 'success';
    } else {
        $_SESSION['message'] = 'เกิดข้อผิดพลาดในการลบห้องพัก: ' . mysqli_error($connect);
        $_SESSION['message_type'] = 'danger';
    }
    
    header('Location: room-management.php');
    exit();
}

function getRoomTypeThai($type) {
    $types = [
        'Standard' => 'ห้องมาตรฐาน',
        'Deluxe' => 'ห้องดีลักซ์',
        'Suite' => 'ห้องสวีท',
        'Family' => 'ห้องครอบครัว',
        'VIP' => 'ห้องวีไอพี'
    ];
    return $types[$type] ?? $type;
}
?>
