// ===== ROOM MANAGEMENT JAVASCRIPT =====

// ฟังก์ชันดูรายละเอียดห้องพัก
function viewRoomDetails(roomId) {
    const modal = document.getElementById('roomModal');
    const modalContent = document.getElementById('roomModalContent');
    
    // ดึงข้อมูลห้องพักจาก DOM
    const roomCard = document.querySelector(`[data-room-id="${roomId}"]`);
    if (roomCard) {
        const roomName = roomCard.querySelector('h3').textContent;
        const roomType = roomCard.querySelector('.room-type').textContent;
        const roomPrice = roomCard.querySelector('.room-price').textContent;
        const roomStatus = roomCard.querySelector('.room-status').textContent;
        const roomImage = roomCard.querySelector('.room-card-img')?.src || '';
        
        modalContent.innerHTML = `
            <div class="modal-header">
                <h2>รายละเอียดห้องพัก</h2>
            </div>
            <div class="modal-body">
                <div class="room-detail-image">
                    <img src="${roomImage}" alt="${roomName}" onerror="this.src='https://via.placeholder.com/400x250/f0f0f0/999999?text=ไม่มีรูปภาพ'">
                </div>
                <div class="room-detail-info">
                    <h3>${roomName}</h3>
                    <p><strong>ประเภท:</strong> ${roomType}</p>
                    <p><strong>ราคา:</strong> ${roomPrice}</p>
                    <p><strong>สถานะ:</strong> <span class="status-badge status-${roomStatus}">${roomStatus}</span></p>
                    <p><strong>รหัสห้อง:</strong> ${roomId}</p>
                </div>
            </div>
            <div class="modal-footer">
                <a href="?page=edit_room_form&id=${roomId}" class="btn btn-primary">
                    <i class="fa-solid fa-edit"></i> แก้ไขห้องพัก
                </a>
                <button class="btn btn-secondary" onclick="closeModal()">
                    <i class="fa-solid fa-times"></i> ปิด
                </button>
            </div>
        `;
        
        modal.style.display = 'block';
    }
}

// ปิด modal
function closeModal() {
    document.getElementById('roomModal').style.display = 'none';
}

// ฟังก์ชันลบห้องพัก
function deleteRoom(roomId, roomName) {
    if (confirm(`คุณแน่ใจหรือไม่ที่จะลบห้องพัก "${roomName}"?`)) {
        window.location.href = `?page=room&action=delete_room&id=${roomId}`;
    }
}

// ฟังก์ชันค้นหาแบบ Real-time
function searchRooms() {
    const searchTerm = document.getElementById('search').value.toLowerCase();
    const roomCards = document.querySelectorAll('.room-card');
    
    roomCards.forEach(card => {
        const roomName = card.querySelector('h3').textContent.toLowerCase();
        const roomType = card.querySelector('.room-type').textContent.toLowerCase();
        
        if (roomName.includes(searchTerm) || roomType.includes(searchTerm)) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}

// ฟังก์ชันกรองห้องพัก
function filterRooms() {
    const typeFilter = document.getElementById('type_filter').value;
    const statusFilter = document.getElementById('status_filter').value;
    const roomCards = document.querySelectorAll('.room-card');
    
    roomCards.forEach(card => {
        const roomType = card.querySelector('.room-type').textContent;
        const roomStatus = card.querySelector('.room-status').textContent;
        
        let showCard = true;
        
        if (typeFilter && roomType !== typeFilter) {
            showCard = false;
        }
        
        if (statusFilter && roomStatus !== statusFilter) {
            showCard = false;
        }
        
        card.style.display = showCard ? 'block' : 'none';
    });
}

// ฟังก์ชันแสดงข้อความแจ้งเตือน
function showMessage(message, type = 'info') {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    messageDiv.innerHTML = `
        <i class="fa-solid fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
        ${message}
    `;
    
    // เพิ่มข้อความที่ด้านบนของหน้า
    const mainContent = document.querySelector('.main-content');
    mainContent.insertBefore(messageDiv, mainContent.firstChild);
    
    // ลบข้อความหลังจาก 5 วินาที
    setTimeout(() => {
        messageDiv.remove();
    }, 5000);
}

// ฟังก์ชันอัปเดตสถิติแบบ Real-time
function updateStatistics() {
    const visibleRooms = document.querySelectorAll('.room-card[style*="display: block"], .room-card:not([style*="display: none"])');
    const totalRooms = visibleRooms.length;
    const availableRooms = Array.from(visibleRooms).filter(card => 
        card.querySelector('.room-status').textContent === 'ว่าง'
    ).length;
    const occupiedRooms = totalRooms - availableRooms;
    
    // อัปเดตสถิติ
    const statCards = document.querySelectorAll('.stat-card h3');
    if (statCards.length >= 3) {
        statCards[0].textContent = totalRooms;
        statCards[1].textContent = availableRooms;
        statCards[2].textContent = occupiedRooms;
    }
}

// ฟังก์ชันตรวจสอบการอัปโหลดรูปภาพ
function validateImageUpload(input) {
    const file = input.files[0];
    const maxSize = 5 * 1024 * 1024; // 5MB
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    
    if (file) {
        if (file.size > maxSize) {
            showMessage('ขนาดไฟล์ต้องไม่เกิน 5MB', 'error');
            input.value = '';
            return false;
        }
        
        if (!allowedTypes.includes(file.type)) {
            showMessage('รองรับเฉพาะไฟล์รูปภาพ (JPG, PNG, GIF, WEBP)', 'error');
            input.value = '';
            return false;
        }
    }
    
    return true;
}

// ฟังก์ชันแสดงตัวอย่างรูปภาพ
function previewImage(input) {
    const preview = document.getElementById('imagePreview');
    const file = input.files[0];
    
    if (file && preview) {
        const reader = new FileReader();
        reader.onload = function(e) {
            preview.src = e.target.result;
            preview.style.display = 'block';
        }
        reader.readAsDataURL(file);
    }
}

// ฟังก์ชันยืนยันการบันทึกข้อมูล
function confirmSave() {
    const form = document.querySelector('form[action*="save_room"]');
    if (form) {
        const roomName = form.querySelector('#name').value;
        const roomPrice = form.querySelector('#price').value;
        
        if (!roomName || !roomPrice) {
            showMessage('กรุณากรอกข้อมูลให้ครบถ้วน', 'error');
            return false;
        }
        
        return confirm('ยืนยันการบันทึกข้อมูลห้องพัก?');
    }
    return true;
}

// Event Listeners
document.addEventListener('DOMContentLoaded', function() {
    // Modal functionality
    const modal = document.getElementById('roomModal');
    const closeBtn = document.querySelector('.close');
    
    if (closeBtn) {
        closeBtn.onclick = closeModal;
    }
    
    // ปิด modal เมื่อคลิกนอก modal
    window.onclick = function(event) {
        if (event.target == modal) {
            closeModal();
        }
    }
    
    // เพิ่ม data-room-id ให้กับ room cards
    const roomCards = document.querySelectorAll('.room-card');
    roomCards.forEach((card, index) => {
        const roomId = card.querySelector('.room-id')?.textContent.replace('ID: ', '') || (index + 1);
        card.setAttribute('data-room-id', roomId);
    });
    
    // Real-time search
    const searchInput = document.getElementById('search');
    if (searchInput) {
        searchInput.addEventListener('input', searchRooms);
    }
    
    // Filter functionality
    const typeFilter = document.getElementById('type_filter');
    const statusFilter = document.getElementById('status_filter');
    
    if (typeFilter) {
        typeFilter.addEventListener('change', filterRooms);
    }
    
    if (statusFilter) {
        statusFilter.addEventListener('change', filterRooms);
    }
    
    // Image upload validation
    const imageInput = document.querySelector('input[type="file"]');
    if (imageInput) {
        imageInput.addEventListener('change', function() {
            if (validateImageUpload(this)) {
                previewImage(this);
            }
        });
    }
    
    // Form validation
    const saveForm = document.querySelector('form[action*="save_room"]');
    if (saveForm) {
        saveForm.addEventListener('submit', function(e) {
            if (!confirmSave()) {
                e.preventDefault();
            }
        });
    }
    
    // Auto-refresh statistics
    setInterval(updateStatistics, 30000);
    
    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // ESC key to close modal
        if (e.key === 'Escape' && modal.style.display === 'block') {
            closeModal();
        }
        
        // Ctrl/Cmd + F to focus search
        if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
            e.preventDefault();
            const searchInput = document.getElementById('search');
            if (searchInput) {
                searchInput.focus();
            }
        }
    });
    
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
});

// ฟังก์ชันสำหรับการทำงานกับ AJAX (ถ้าต้องการ)
function loadRoomData(roomId) {
    fetch(`api/room.php?id=${roomId}`)
        .then(response => response.json())
        .then(data => {
            // จัดการข้อมูลที่ได้รับ
            console.log('Room data:', data);
        })
        .catch(error => {
            console.error('Error loading room data:', error);
            showMessage('เกิดข้อผิดพลาดในการโหลดข้อมูล', 'error');
        });
}

// ฟังก์ชันสำหรับการอัปโหลดรูปภาพแบบ AJAX
function uploadImage(formData) {
    return fetch('api/upload.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('อัปโหลดรูปภาพสำเร็จ', 'success');
            return data.filename;
        } else {
            throw new Error(data.message);
        }
    })
    .catch(error => {
        showMessage('เกิดข้อผิดพลาดในการอัปโหลด: ' + error.message, 'error');
        throw error;
    });
} 