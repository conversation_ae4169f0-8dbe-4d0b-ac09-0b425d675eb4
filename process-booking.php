<?php
// process-booking.php (แก้ไขแล้ว)

session_start();
require('connect.php'); // ไฟล์เชื่อมต่อฐานข้อมูลของคุณ

// --- ตรวจสอบว่าเป็นการส่งข้อมูลแบบ POST หรือไม่ ---
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    // ถ้าไม่ใช่ ให้เด้งกลับไปหน้าหลัก
    header('Location: index.php'); // หรือหน้าที่คุณต้องการ
    exit;
}

// --- ตรวจสอบการล็อกอิน ---
if (!isset($_SESSION['id_account'])) {
    http_response_code(403);
    exit('Unauthorized');
}

// --- รับข้อมูลจากฟอร์ม ---
$room_id = $_POST['room_id'] ?? '';
$customer_name = $_POST['full_name'] ?? '';
$customer_phone = $_POST['phone'] ?? '';
$check_in_date = $_POST['checkin_date'] ?? '';
$check_out_date = $_POST['checkout_date'] ?? '';
$guests = $_POST['guests'] ?? '';
$price_per_night = $_POST['price_per_night'] ?? '';
$total_price = 0;
$deposit_amount = 0;

// --- การคำนวณราคาที่ฝั่ง Server (เพื่อความปลอดภัย) ---
if ($check_in_date && $check_out_date && $price_per_night) {
    $date1 = new DateTime($check_in_date);
    $date2 = new DateTime($check_out_date);
    $interval = $date1->diff($date2);
    $nights = $interval->days;

    if ($nights <= 0) {
        die("ข้อมูลวันที่ไม่ถูกต้อง");
    }

    $total_price = $nights * $price_per_night;
    $deposit_amount = $total_price * 0.5;
}

// --- จัดการการอัปโหลดไฟล์สลิป ---
$slip_image_url = '';
if (isset($_FILES['slip_upload']) && $_FILES['slip_upload']['error'] == UPLOAD_ERR_OK) {
    $target_dir = "uploads/slips/";
    if (!is_dir($target_dir)) {
        mkdir($target_dir, 0777, true);
    }
    $filename = uniqid() . '_' . basename($_FILES['slip_upload']['name']);
    $target_file = $target_dir . $filename;
    $file_type = strtolower(pathinfo($target_file, PATHINFO_EXTENSION));
    $allowed_types = ['jpg', 'jpeg', 'png', 'gif'];
    if (in_array($file_type, $allowed_types)) {
        if (move_uploaded_file($_FILES['slip_upload']['tmp_name'], $target_file)) {
            $slip_image_url = $target_file;
        }
    }
} else {
    die("กรุณาแนบสลิปการชำระเงิน");
}

// --- บันทึกข้อมูลลงฐานข้อมูล ---
$stmt = $connect->prepare(
    "INSERT INTO bookings (room_id, customer_name, customer_phone, check_in_date, check_out_date, guests, total_price, deposit_amount, slip_image_url, status) 
     VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'รออนุมัติ')"
);

if ($stmt === false) {
    die("Prepare failed: " . htmlspecialchars($connect->error));
}

$stmt->bind_param(
    'issssidds',
    $room_id,
    $customer_name,
    $customer_phone,
    $check_in_date,
    $check_out_date,
    $guests,
    $total_price,
    $deposit_amount,
    $slip_image_url
);

if ($stmt->execute()) {
    http_response_code(200);
    echo 'success';
} else {
    http_response_code(500);
    echo 'error';
}



$stmt->close();
$connect->close();
?>