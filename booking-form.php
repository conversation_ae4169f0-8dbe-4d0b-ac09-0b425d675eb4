<?php
session_start();
require('connect.php');

// ตรวจสอบการล็อกอิน
if (!isset($_SESSION['id_account']) || !isset($_SESSION['role_account'])) {
    header('Location: form-login.php');
    exit();
}

// รับข้อมูลห้องจาก URL parameter
$room_id = isset($_GET['room_id']) ? (int)$_GET['room_id'] : 0;

if ($room_id <= 0) {
    header('Location: index.php');
    exit();
}

// ดึงข้อมูลห้องพัก
$query = "SELECT * FROM room WHERE id_room = ? AND status = 'available'";
$stmt = $connect->prepare($query);
$stmt->bind_param('i', $room_id);
$stmt->execute();
$result = $stmt->get_result();
$room = $result->fetch_assoc();

if (!$room) {
    header('Location: index.php');
    exit();
}

// ดึงข้อมูลผู้ใช้
$id_account = $_SESSION['id_account'];
$query_user = "SELECT username_account FROM account WHERE id_account = ?";
$stmt_user = $connect->prepare($query_user);
$stmt_user->bind_param('i', $id_account);
$stmt_user->execute();
$result_user = $stmt_user->get_result();
$user = $result_user->fetch_assoc();

// สร้าง array รูปภาพสำหรับสไลด์ (ถ้ามีรูปเพิ่มเติม)
$room_images = [];
if (!empty($room['image'])) {
    $room_images[] = $room['image'];
}
// เพิ่มรูปตัวอย่างเพิ่มเติม (คุณสามารถดึงจากฐานข้อมูลได้)
$room_images[] = 'uploads/room_1753895115_688a50cbc9e42.jpg';
$room_images[] = 'uploads/room_1753895206_688a5126c67e4.webp';
$room_images[] = 'uploads/room_1753895281_688a51713f18d.jpg';
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>จองห้องพัก - <?php echo htmlspecialchars($room['room_number']); ?></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Sarabun:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #7b43ff;
            --primary-hover: #6a35e8;
            --primary-light: #f3f0ff;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --text-color: #2d3748;
            --text-secondary: #718096;
            --border-color: #e2e8f0;
            --bg-light: #f7fafc;
            --white: #ffffff;
            --shadow-sm: 0 1px 3px rgba(0,0,0,0.1);
            --shadow-md: 0 4px 6px rgba(0,0,0,0.1);
            --shadow-lg: 0 10px 25px rgba(0,0,0,0.1);
            --shadow-xl: 0 20px 40px rgba(0,0,0,0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Sarabun', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: var(--text-color);
            line-height: 1.6;
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        /* Header */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 20px 0;
            box-shadow: var(--shadow-md);
            margin-bottom: 30px;
            border-radius: 0 0 20px 20px;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .back-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            padding: 10px 20px;
            border-radius: 25px;
            transition: all 0.3s ease;
            background: var(--primary-light);
        }

        .back-btn:hover {
            background: var(--primary-color);
            color: white;
            transform: translateX(-5px);
        }

        .page-title {
            font-size: 28px;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary-color), #9f7aea);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* Main Content */
        .booking-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }

        /* Room Details Card */
        .room-details {
            background: var(--white);
            border-radius: 24px;
            overflow: hidden;
            box-shadow: var(--shadow-xl);
            transition: transform 0.3s ease;
        }

        .room-details:hover {
            transform: translateY(-5px);
        }

        /* Image Slider */
        .image-slider {
            position: relative;
            height: 400px;
            overflow: hidden;
        }

        .slider-container {
            display: flex;
            transition: transform 0.5s ease;
            height: 100%;
        }

        .slider-image {
            min-width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .slider-nav {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 10px;
            z-index: 10;
        }

        .slider-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.5);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .slider-dot.active {
            background: var(--primary-color);
            transform: scale(1.2);
        }

        .slider-arrow {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.9);
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-color);
            font-size: 18px;
            transition: all 0.3s ease;
            z-index: 10;
        }

        .slider-arrow:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-50%) scale(1.1);
        }

        .slider-arrow.prev {
            left: 20px;
        }

        .slider-arrow.next {
            right: 20px;
        }

        .room-info {
            padding: 40px;
        }

        .room-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 25px;
        }

        .room-title {
            font-size: 32px;
            font-weight: 700;
            color: var(--text-color);
            margin-bottom: 10px;
        }

        .room-type {
            background: linear-gradient(135deg, var(--primary-color), #9f7aea);
            color: white;
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
        }

        .room-price {
            font-size: 36px;
            font-weight: 700;
            background: linear-gradient(135deg, var(--success-color), #48bb78);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 20px;
        }

        .room-description {
            color: var(--text-secondary);
            margin-bottom: 30px;
            line-height: 1.7;
            font-size: 16px;
        }

        .amenities {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }

        .amenity-item {
            display: flex;
            align-items: center;
            gap: 12px;
            color: var(--text-color);
            padding: 15px;
            background: var(--bg-light);
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .amenity-item:hover {
            background: var(--primary-light);
            transform: translateX(5px);
        }

        .amenity-icon {
            width: 24px;
            height: 24px;
            color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .status-badge {
            background: linear-gradient(135deg, var(--success-color), #48bb78);
            color: white;
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            box-shadow: var(--shadow-sm);
        }

        /* Booking Form */
        .booking-form {
            background: var(--white);
            border-radius: 24px;
            padding: 40px;
            box-shadow: var(--shadow-xl);
            position: relative;
            overflow: hidden;
        }

        .booking-form::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, var(--primary-color), #9f7aea);
        }

        .form-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 35px;
            color: var(--text-color);
            text-align: center;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--text-color);
            font-size: 16px;
        }

        .form-input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid var(--border-color);
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            font-family: inherit;
            background: var(--bg-light);
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            background: white;
            box-shadow: 0 0 0 3px rgba(123, 67, 255, 0.1);
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .price-summary {
            background: linear-gradient(135deg, var(--primary-light), #e9d8fd);
            border-radius: 16px;
            padding: 30px;
            margin-bottom: 35px;
            border: 2px solid var(--primary-color);
        }

        .price-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            font-size: 16px;
        }

        .price-row:last-child {
            border-top: 2px solid var(--primary-color);
            padding-top: 20px;
            margin-top: 20px;
            font-weight: 700;
            font-size: 20px;
            color: var(--primary-color);
        }

        .file-upload {
            position: relative;
            display: inline-block;
            width: 100%;
        }

        .file-upload input[type=file] {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }

        .file-upload-label {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            padding: 20px;
            border: 2px dashed var(--primary-color);
            border-radius: 12px;
            background: var(--primary-light);
            color: var(--primary-color);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .file-upload-label:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }

        .submit-btn {
            width: 100%;
            background: linear-gradient(135deg, var(--primary-color), #9f7aea);
            color: white;
            border: none;
            padding: 18px;
            border-radius: 12px;
            font-size: 18px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            box-shadow: var(--shadow-md);
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .submit-btn:disabled {
            background: var(--text-secondary);
            cursor: not-allowed;
            transform: none;
        }

        /* Success Modal */
        .modal-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            backdrop-filter: blur(5px);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: var(--white);
            border-radius: 24px;
            padding: 50px;
            text-align: center;
            max-width: 500px;
            width: 90%;
            box-shadow: var(--shadow-xl);
            animation: modalSlideIn 0.5s ease;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .modal-icon {
            font-size: 80px;
            color: var(--success-color);
            margin-bottom: 25px;
            animation: bounce 1s ease;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        .modal-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 20px;
            color: var(--text-color);
        }

        .modal-message {
            color: var(--text-secondary);
            margin-bottom: 35px;
            line-height: 1.7;
            font-size: 16px;
        }

        .modal-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .btn {
            padding: 15px 30px;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            font-size: 16px;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), #9f7aea);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-secondary {
            background: var(--bg-light);
            color: var(--text-color);
            border: 2px solid var(--border-color);
        }

        .btn-secondary:hover {
            background: var(--border-color);
            transform: translateY(-2px);
        }

        /* Loading Animation */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Responsive */
        @media (max-width: 1024px) {
            .booking-container {
                grid-template-columns: 1fr;
                gap: 30px;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }

            .amenities {
                grid-template-columns: 1fr;
            }

            .modal-buttons {
                flex-direction: column;
            }

            .room-title {
                font-size: 24px;
            }

            .page-title {
                font-size: 20px;
            }
        }

        /* Smooth scrolling */
        html {
            scroll-behavior: smooth;
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--bg-light);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--primary-hover);
        }

        /* File Upload Styles */
        .file-upload-container {
            margin-top: 10px;
        }

        .file-upload-area {
            border: 3px dashed var(--primary-color);
            border-radius: 15px;
            padding: 40px 20px;
            text-align: center;
            background: var(--primary-light);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .file-upload-area:hover {
            border-color: var(--primary-hover);
            background: rgba(123, 67, 255, 0.05);
            transform: translateY(-2px);
        }

        .file-upload-area.dragover {
            border-color: var(--success-color);
            background: rgba(40, 167, 69, 0.1);
            transform: scale(1.02);
        }

        .upload-icon {
            font-size: 48px;
            color: var(--primary-color);
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }

        .file-upload-area:hover .upload-icon {
            transform: scale(1.1);
            color: var(--primary-hover);
        }

        .upload-text h4 {
            color: var(--text-color);
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .upload-text p {
            color: var(--text-secondary);
            font-size: 14px;
            margin: 0;
        }

        .file-preview {
            margin-top: 20px;
            animation: slideIn 0.3s ease;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .preview-container {
            position: relative;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: var(--shadow-md);
            background: white;
        }

        .preview-container img {
            width: 100%;
            height: 200px;
            object-fit: cover;
            display: block;
        }

        .preview-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .preview-container:hover .preview-overlay {
            opacity: 1;
        }

        .remove-file {
            background: var(--danger-color);
            color: white;
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .remove-file:hover {
            background: #c82333;
            transform: scale(1.1);
        }

        .file-info {
            background: white;
            padding: 15px;
            border-radius: 0 0 12px 12px;
            border-top: 1px solid var(--border-color);
        }

        .file-info span {
            display: block;
            font-size: 14px;
            color: var(--text-secondary);
        }

        .file-info span:first-child {
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 5px;
        }

        /* Loading Animation */
        .upload-loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 255, 255, 0.9);
            padding: 20px;
            border-radius: 10px;
            display: none;
        }

        .loading-spinner {
            width: 30px;
            height: 30px;
            border: 3px solid var(--primary-light);
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* QR Code Styles */
        .qr-section {
            margin-top: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            box-shadow: var(--shadow-lg);
        }

        .qr-container {
            background: white;
            border-radius: 12px;
            padding: 25px;
            text-align: center;
            box-shadow: var(--shadow-md);
        }

        .qr-header {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            color: var(--primary-color);
        }

        .qr-header i {
            font-size: 24px;
            margin-right: 10px;
        }

        .qr-header h4 {
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }

        .qr-content {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .qr-code {
            flex-shrink: 0;
            width: 120px;
            height: 120px;
            background: white;
            border: 2px solid var(--border-color);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 10px;
            box-shadow: var(--shadow-sm);
        }

        .qr-code img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .qr-info {
            flex: 1;
            text-align: left;
        }

        .qr-info p {
            margin: 0 0 8px 0;
            color: var(--text-color);
            font-size: 14px;
        }

        .qr-info p:first-child {
            font-weight: 600;
            color: var(--primary-color);
            font-size: 16px;
        }

        .qr-url {
            font-size: 12px !important;
            color: var(--text-secondary) !important;
            word-break: break-all;
            background: var(--bg-light);
            padding: 8px;
            border-radius: 6px;
            margin-top: 10px !important;
        }

        /* QR Code Animation */
        .qr-code {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(123, 67, 255, 0.4);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(123, 67, 255, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(123, 67, 255, 0);
            }
        }

        /* Responsive QR Code */
        @media (max-width: 768px) {
            .qr-content {
                flex-direction: column;
                gap: 15px;
            }

            .qr-code {
                width: 100px;
                height: 100px;
            }

            .qr-info {
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="container">
            <div class="header-content">
                <a href="index.php" class="back-btn">
                    <i class="fas fa-arrow-left"></i>
                    กลับสู่หน้าหลัก
                </a>
                <h1 class="page-title">จองห้องพัก</h1>
                <div></div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="booking-container">
            <!-- Room Details -->
            <div class="room-details">
                <!-- Image Slider -->
                <div class="image-slider">
                    <div class="slider-container" id="sliderContainer">
                        <?php foreach ($room_images as $index => $image): ?>
                            <img src="<?php echo htmlspecialchars($image); ?>" alt="ห้อง <?php echo htmlspecialchars($room['room_number']); ?> - รูปที่ <?php echo $index + 1; ?>" class="slider-image">
                        <?php endforeach; ?>
                    </div>
                    
                    <!-- Slider Navigation -->
                    <button class="slider-arrow prev" onclick="changeSlide(-1)">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <button class="slider-arrow next" onclick="changeSlide(1)">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                    
                    <div class="slider-nav" id="sliderNav">
                        <?php foreach ($room_images as $index => $image): ?>
                            <div class="slider-dot <?php echo $index === 0 ? 'active' : ''; ?>" onclick="goToSlide(<?php echo $index; ?>)"></div>
                        <?php endforeach; ?>
                    </div>
                </div>

                <div class="room-info">
                    <div class="room-header">
                        <div>
                            <h2 class="room-title">ห้อง <?php echo htmlspecialchars($room['room_number']); ?></h2>
                            <span class="room-type"><?php echo htmlspecialchars($room['room_type']); ?></span>
                        </div>
                        <span class="status-badge">ว่าง</span>
                    </div>
                    
                    <div class="room-price">฿<?php echo number_format($room['price']); ?></div>
                    
                    <p class="room-description">
                        <?php echo htmlspecialchars($room['description'] ?: 'ห้องพักที่สะดวกสบาย พร้อมสิ่งอำนวยความสะดวกครบครัน ดีไซน์ทันสมัย เน้นความสะดวกสบายและความเป็นส่วนตัว'); ?>
                    </p>
                    
                    <div class="amenities">
                        <div class="amenity-item">
                            <i class="fas fa-wifi amenity-icon"></i>
                            <span>WiFi ฟรี</span>
                        </div>
                        <div class="amenity-item">
                            <i class="fas fa-snowflake amenity-icon"></i>
                            <span>แอร์คอนดิชัน</span>
                        </div>
                        <div class="amenity-item">
                            <i class="fas fa-tv amenity-icon"></i>
                            <span>ทีวี LED</span>
                        </div>
                        <div class="amenity-item">
                            <i class="fas fa-bath amenity-icon"></i>
                            <span>ห้องน้ำในตัว</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Booking Form -->
            <div class="booking-form">
                <h2 class="form-title">ข้อมูลการจอง</h2>
                
                <form id="bookingForm" action="process-booking.php" method="POST" enctype="multipart/form-data">
                    <input type="hidden" name="room_id" value="<?php echo $room['id_room']; ?>">
                    <input type="hidden" name="price_per_night" value="<?php echo $room['price']; ?>">
                    
                    <div class="form-group">
                        <label class="form-label">ชื่อ-นามสกุล *</label>
                        <input type="text" name="full_name" class="form-input" value="<?php echo htmlspecialchars($user['username_account']); ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">เบอร์โทรศัพท์ *</label>
                        <input type="tel" name="phone" class="form-input" required>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">วันที่เช็คอิน *</label>
                            <input type="date" name="checkin_date" class="form-input" required min="<?php echo date('Y-m-d'); ?>">
                        </div>
                        <div class="form-group">
                            <label class="form-label">วันที่เช็คเอาท์ *</label>
                            <input type="date" name="checkout_date" class="form-input" required min="<?php echo date('Y-m-d', strtotime('+1 day')); ?>">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">จำนวนผู้เข้าพัก *</label>
                        <select name="guests" class="form-input" required>
                            <option value="">เลือกจำนวนผู้เข้าพัก</option>
                            <option value="1">1 คน</option>
                            <option value="2">2 คน</option>
                            <option value="3">3 คน</option>
                            <option value="4">4 คน</option>
                            <option value="5">5 คน</option>
                        </select>
                    </div>
                    
                    <div class="price-summary">
                        <div class="price-row">
                            <span>ราคาต่อคืน:</span>
                            <span>฿<?php echo number_format($room['price']); ?></span>
                        </div>
                        <div class="price-row">
                            <span>จำนวนคืน:</span>
                            <span id="nights">-</span>
                        </div>
                        <div class="price-row">
                            <span>ราคารวม:</span>
                            <span id="total-price">-</span>
                        </div>
                        <div class="price-row">
                            <span>เงินมัดจำ (50%):</span>
                            <span id="deposit">-</span>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="slip_upload" class="form-label">
                            <i class="fas fa-receipt"></i>
                            สลิปการชำระเงิน *
                        </label>
                        
                        <div class="file-upload-container">
                            <div class="file-upload-area" id="uploadArea">
                                <div class="upload-icon">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                </div>
                                <div class="upload-text">
                                    <h4>อัปโหลดสลิปการชำระเงิน</h4>
                                    <p>ลากไฟล์มาวางที่นี่ หรือคลิกเพื่อเลือกไฟล์</p>
                                </div>
                                <input type="file" name="slip_upload" id="slipUpload" accept="image/*" required style="display: none;">
                            </div>
                            
                            <div class="file-preview" id="filePreview" style="display: none;">
                                <div class="preview-container">
                                    <img id="previewImage" src="" alt="Preview">
                                    <div class="preview-overlay">
                                        <button type="button" class="remove-file" onclick="removeFile()">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="file-info">
                                    <span id="fileName"></span>
                                    <span id="fileSize"></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- QR Code Section -->
                    <div class="qr-section">
                        <div class="qr-container">
                            <div class="qr-header">
                                <i class="fas fa-qrcode"></i>
                                <h4>สแกน QR Code</h4>
                            </div>
                            <div class="qr-content">
                                <div class="qr-code" id="qrCode">
                                    <!-- QR Code จะถูกสร้างด้วย JavaScript -->
                                </div>
                                <div class="qr-info">
                                    <p>สแกนเพื่อเข้าถึงระบบจองห้องพัก</p>
                                    <p class="qr-url"><?php echo (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]"; ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <button type="submit" class="submit-btn" id="submitBtn">
                        <i class="fas fa-calendar-check"></i>
                        ยืนยันการจอง
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Success Modal -->
    <div class="modal-overlay" id="successModal">
        <div class="modal-content">
            <i class="fas fa-check-circle modal-icon"></i>
            <h3 class="modal-title">จองสำเร็จ!</h3>
            <p class="modal-message">
                การจองของท่านได้ถูกส่งเข้าระบบเรียบร้อยแล้ว<br>
                และกำลังรอการอนุมัติจากเจ้าหน้าที่
            </p>
            <div class="modal-buttons">
                <a href="index.php" class="btn btn-secondary">กลับสู่หน้าหลัก</a>
                <a href="history.php" class="btn btn-primary">ดูประวัติการจอง</a>
            </div>
        </div>
    </div>

    <script>
        // Image Slider
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slider-image');
        const dots = document.querySelectorAll('.slider-dot');
        const totalSlides = slides.length;

        function showSlide(n) {
            currentSlide = (n + totalSlides) % totalSlides;
            
            const container = document.getElementById('sliderContainer');
            container.style.transform = `translateX(-${currentSlide * 100}%)`;
            
            // Update dots
            dots.forEach((dot, index) => {
                dot.classList.toggle('active', index === currentSlide);
            });
        }

        function changeSlide(direction) {
            showSlide(currentSlide + direction);
        }

        function goToSlide(n) {
            showSlide(n);
        }

        // Auto slide every 5 seconds
        setInterval(() => {
            changeSlide(1);
        }, 5000);

        // File Upload Enhancement
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('slipUpload');
        const filePreview = document.getElementById('filePreview');
        const previewImage = document.getElementById('previewImage');
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');

        // Click to upload
        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });

        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });

        // File input change
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFile(e.target.files[0]);
            }
        });

        function handleFile(file) {
            // Validate file type
            const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
            if (!validTypes.includes(file.type)) {
                alert('กรุณาเลือกไฟล์รูปภาพเท่านั้น (JPG, JPEG, PNG, GIF)');
                return;
            }

            // Validate file size (5MB)
            if (file.size > 5 * 1024 * 1024) {
                alert('ขนาดไฟล์ต้องไม่เกิน 5MB');
                return;
            }

            // Show preview
            const reader = new FileReader();
            reader.onload = (e) => {
                previewImage.src = e.target.result;
                fileName.textContent = file.name;
                fileSize.textContent = formatFileSize(file.size);
                
                uploadArea.style.display = 'none';
                filePreview.style.display = 'block';
            };
            reader.readAsDataURL(file);
        }

        function removeFile() {
            fileInput.value = '';
            uploadArea.style.display = 'block';
            filePreview.style.display = 'none';
            previewImage.src = '';
            fileName.textContent = '';
            fileSize.textContent = '';
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // คำนวณราคา
        function calculatePrice() {
            const checkinDate = document.querySelector('input[name="checkin_date"]').value;
            const checkoutDate = document.querySelector('input[name="checkout_date"]').value;
            const pricePerNight = <?php echo $room['price']; ?>;
            
            if (checkinDate && checkoutDate) {
                const checkin = new Date(checkinDate);
                const checkout = new Date(checkoutDate);
                const diffTime = checkout - checkin;
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                
                if (diffDays > 0) {
                    const totalPrice = diffDays * pricePerNight;
                    const deposit = totalPrice * 0.5;
                    
                    document.getElementById('nights').textContent = diffDays + ' คืน';
                    document.getElementById('total-price').textContent = '฿' + totalPrice.toLocaleString();
                    document.getElementById('deposit').textContent = '฿' + deposit.toLocaleString();
                } else {
                    document.getElementById('nights').textContent = '-';
                    document.getElementById('total-price').textContent = '-';
                    document.getElementById('deposit').textContent = '-';
                }
            }
        }

        // เพิ่ม event listeners
        document.querySelector('input[name="checkin_date"]').addEventListener('change', calculatePrice);
        document.querySelector('input[name="checkout_date"]').addEventListener('change', calculatePrice);

        // จัดการการส่งฟอร์ม
        document.getElementById('bookingForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<div class="loading-spinner"></div> กำลังส่งข้อมูล...';
            
            // ส่งฟอร์ม
            const formData = new FormData(this);
            
            fetch('process-booking.php', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (response.ok) {
                    // แสดง modal สำเร็จ
                    document.getElementById('successModal').style.display = 'flex';
                } else {
                    throw new Error('เกิดข้อผิดพลาดในการจอง');
                }
            })
            .catch(error => {
                alert('เกิดข้อผิดพลาด: ' + error.message);
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-calendar-check"></i> ยืนยันการจอง';
            });
        });

        // ปิด modal เมื่อคลิกพื้นหลัง
        document.getElementById('successModal').addEventListener('click', function(e) {
            if (e.target === this) {
                this.style.display = 'none';
            }
        });

        // Smooth animations on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe elements for animation
        document.querySelectorAll('.room-details, .booking-form').forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(30px)';
            el.style.transition = 'all 0.6s ease';
            observer.observe(el);
        });

        // QR Code Generation
        function generateQRCode() {
            const currentURL = window.location.href;
            const qrContainer = document.getElementById('qrCode');
            
            // ใช้ QR Server API เพื่อสร้าง QR Code
            const qrImageUrl = `https://api.qrserver.com/v1/create-qr-code/?size=100x100&data=${encodeURIComponent(currentURL)}`;
            
            const qrImage = document.createElement('img');
            qrImage.src = qrImageUrl;
            qrImage.alt = 'QR Code for booking page';
            qrImage.style.width = '100%';
            qrImage.style.height = '100%';
            
            // เพิ่ม loading state
            qrImage.onload = function() {
                qrContainer.innerHTML = '';
                qrContainer.appendChild(qrImage);
            };
            
            qrImage.onerror = function() {
                qrContainer.innerHTML = `
                    <div style="color: var(--text-secondary); font-size: 12px; text-align: center;">
                        <i class="fas fa-qrcode" style="font-size: 24px; margin-bottom: 5px;"></i>
                        <br>QR Code
                    </div>
                `;
            };
        }

        // สร้าง QR Code เมื่อหน้าโหลดเสร็จ
        document.addEventListener('DOMContentLoaded', function() {
            generateQRCode();
        });

        // เพิ่มฟังก์ชันสำหรับดาวน์โหลด QR Code
        function downloadQRCode() {
            const currentURL = window.location.href;
            const qrImageUrl = `https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=${encodeURIComponent(currentURL)}`;
            
            const link = document.createElement('a');
            link.href = qrImageUrl;
            link.download = 'booking-qr-code.png';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // เพิ่มปุ่มดาวน์โหลด QR Code (ถ้าต้องการ)
        function addDownloadButton() {
            const qrContainer = document.querySelector('.qr-container');
            const downloadBtn = document.createElement('button');
            downloadBtn.innerHTML = '<i class="fas fa-download"></i> ดาวน์โหลด QR Code';
            downloadBtn.className = 'download-qr-btn';
            downloadBtn.onclick = downloadQRCode;
            downloadBtn.style.cssText = `
                background: var(--primary-color);
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-size: 12px;
                cursor: pointer;
                margin-top: 10px;
                transition: all 0.3s ease;
            `;
            downloadBtn.onmouseover = function() {
                this.style.background = 'var(--primary-hover)';
            };
            downloadBtn.onmouseout = function() {
                this.style.background = 'var(--primary-color)';
            };
            
            qrContainer.appendChild(downloadBtn);
        }

        // เพิ่มปุ่มดาวน์โหลด (ถ้าต้องการ)
        // addDownloadButton();
    </script>
</body>
</html>