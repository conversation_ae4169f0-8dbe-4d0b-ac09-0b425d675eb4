<?php
session_start();
require('connect.php'); 

// ตรวจสอบสิทธิ์การเข้าใช้งาน (owner เท่านั้น)
if (!isset($_SESSION['id_account']) || !isset($_SESSION['role_account']) || $_SESSION['role_account'] !== 'owner') {
    die(header('Location: form-login.php'));
}

$id_account = $_SESSION['id_account'];
$query_show = "SELECT username_account, role_account, images_account FROM account WHERE id_account = '$id_account'";
$call_back_show = mysqli_query($connect, $query_show);
$result_show = mysqli_fetch_assoc($call_back_show);

// --- ดึงข้อมูลสถิติหลัก ๆ ---
$stats = [];
$result_total_booking = $connect->query("SELECT COUNT(*) AS total FROM bookings");
$stats['total_bookings'] = $result_total_booking->fetch_assoc()['total'] ?? 0;

$result_total_income = $connect->query("SELECT SUM(total_price) AS income FROM bookings WHERE status = 'อนุมัติแล้ว'");
$stats['total_income'] = $result_total_income->fetch_assoc()['income'] ?? 0;

$result_total_customer = $connect->query("SELECT COUNT(DISTINCT customer_name) AS customer FROM bookings");
$stats['total_customers'] = $result_total_customer->fetch_assoc()['customer'] ?? 0;

$result_total_room = $connect->query("SELECT COUNT(*) AS room FROM rooms");
$stats['total_rooms'] = $result_total_room->fetch_assoc()['room'] ?? 0;

$result_room_available = $connect->query("SELECT COUNT(*) AS available FROM rooms WHERE status = 'ว่าง'");
$stats['available_rooms'] = $result_room_available->fetch_assoc()['available'] ?? 0;

$result_room_unavailable = $connect->query("SELECT COUNT(*) AS unavailable FROM rooms WHERE status = 'ไม่ว่าง'");
$stats['unavailable_rooms'] = $result_room_unavailable->fetch_assoc()['unavailable'] ?? 0;

// สำหรับ active menu
$current_page_file = basename($_SERVER['SCRIPT_NAME']);
switch ($current_page_file) {
    case 'owner.php':
        $currentPage = 'dashboard';
        break;
    case 'statistics.php':
        $currentPage = 'statistics';
        break;
    default:
        $currentPage = '';
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Owner Dashboard - ระบบจัดการรีสอร์ท</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Sarabun:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        /* --- คัดลอก style จาก index.php --- */
        :root {
            --primary-color: #7b43ff;
            --light-primary-color: #f3f0ff;
            --text-color: #495057;
            --text-secondary-color: #6c757d;
            --sidebar-bg: #ffffff;
            --main-bg: #f9fafb;
            --border-color: #e7e7e7;
        }
        body { font-family: 'Sarabun', sans-serif; margin: 0; background-color: var(--main-bg); color: var(--text-color);}
        .main-content { margin-left: 280px; padding: 30px;}
        .sidebar {
            width: 280px; height: 100vh; position: fixed; top: 0; left: 0;
            background-color: var(--sidebar-bg); padding: 20px; display: flex; flex-direction: column;
            box-sizing: border-box; border-right: 1px solid var(--border-color);
        }
        .sidebar-header { padding: 0 10px; margin-bottom: 25px; display: flex; align-items: center;}
        .logo { display: flex; align-items: center; font-size: 22px; font-weight: 700; color: #343a40; text-decoration: none;}
        .logo i { color: var(--primary-color); font-size: 30px; margin-right: 12px;}
        .user-profile { display: flex; align-items: center; padding: 15px 10px; margin-bottom: 15px; border-top: 1px solid var(--border-color); border-bottom: 1px solid var(--border-color);}
        .user-avatar { width: 45px; height: 45px; border-radius: 50%; background-color: var(--primary-color); color: white; display: flex; align-items: center; justify-content: center; font-size: 22px; margin-right: 15px;}
        .user-info h5 { margin: 0; font-weight: 700; font-size: 16px;}
        .user-info p { margin: 0; font-size: 14px; color: var(--text-secondary-color);}
        .sidebar-nav { list-style: none; padding: 0; margin: 0; flex-grow: 1;}
        .nav-header { padding: 15px 15px 8px; font-size: 12px; font-weight: 700; color: #adb5bd; text-transform: uppercase;}
        .sidebar-nav li a { display: flex; align-items: center; padding: 12px 15px; margin: 4px 0; color: var(--text-color); text-decoration: none; font-size: 16px; font-weight: 500; border-radius: 8px; transition: all 0.2s;}
        .sidebar-nav li a i { width: 20px; margin-right: 15px; text-align: center; font-size: 18px; color: var(--text-secondary-color); transition: color 0.2s;}
        .sidebar-nav li.active a { background-color: var(--primary-color); color: #fff; font-weight: 700; box-shadow: 0 4px 10px rgba(123, 67, 255, 0.3);}
        .sidebar-nav li.active a i { color: #fff;}
        .sidebar-nav li a:hover { background-color: var(--light-primary-color); color: var(--primary-color);}
        .sidebar-nav li a:hover i { color: var(--primary-color);}
        .sidebar-nav li.active a:hover { background-color: var(--primary-color); color: #fff; cursor: default;}
        .sidebar-nav .logout-item { margin-top: auto;}
        /* --- เพิ่มเติม --- */
        .dashboard-welcome { font-size: 2em; font-weight: 700; margin-bottom: 20px;}
        .dashboard-desc { font-size: 1.1em; color: var(--text-secondary-color);}
        .stat-link { display: inline-block; margin-top: 30px; padding: 15px 30px; background: var(--primary-color); color: #fff; border-radius: 8px; text-decoration: none; font-weight: 600; font-size: 1.1em; transition: background 0.2s;}
        .stat-link:hover { background: #5e2ecf;}
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }
        .stat-card {
            background: #fff;
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.07);
            border: 1px solid #e7e7e7;
            text-align: center;
        }
        .stat-icon {
            width: 50px; height: 50px; border-radius: 12px;
            display: flex; align-items: center; justify-content: center;
            font-size: 24px; margin: 0 auto 15px auto; color: var(--primary-color); background: var(--light-primary-color);
        }
        .stat-number { font-size: 2em; font-weight: 700; margin-bottom: 5px; }
        .stat-label { color: var(--text-secondary-color); font-size: 1.1em; }
        .stat-icon.income { color: #28a745; }
        .stat-icon.customer { color: #fd7e14; }
        .stat-icon.room { color: #17a2b8; }
        .stat-icon.available { color: #28a745; }
        .stat-icon.occupied { color: #dc3545; }
    </style>
</head>
<body>
    <aside class="sidebar">
        <div class="sidebar-header">
            <a href="owner.php" class="logo">
                <i class="fa-solid fa-hotel"></i>
                <span>ระบบจัดการรีสอร์ท</span>
            </a>
        </div>
        <div class="user-profile">
            <div class="user-avatar">
                <i class="fa-solid fa-user"></i>
            </div>
            <div class="user-info">
                <h5><?php echo htmlspecialchars($result_show['username_account']); ?></h5>
                <p><?php echo htmlspecialchars($result_show['role_account']); ?></p>
            </div>
        </div>
        <ul class="sidebar-nav">
            <li class="nav-header">เมนูเจ้าของ</li>
            <li class="<?php if ($currentPage == 'dashboard') echo 'active'; ?>">
                <a href="owner.php"><i class="fa-solid fa-gauge"></i><span>แดชบอร์ด</span></a>
            </li>
            <li class="<?php if ($currentPage == 'statistics') echo 'active'; ?>">
                <a href="statistics.php"><i class="fa-solid fa-chart-line"></i><span>รายงานสถิติ</span></a>
            </li>
            <li class="logout-item">
                <a href="form-login.php?logout=1"><i class="fa-solid fa-right-from-bracket"></i><span>ออกจากระบบ</span></a>
            </li>
        </ul>
    </aside>
    <main class="main-content">
        <div class="dashboard-welcome">
            <i class="fa-solid fa-gauge"></i> ยินดีต้อนรับเจ้าของรีสอร์ท
                    </div>
        <div class="dashboard-desc">
            คุณสามารถดูรายงานสถิติการเข้าใช้ระบบและจัดการข้อมูลต่าง ๆ ได้จากเมนูด้านซ้าย
                                        </div>
        <!-- เพิ่มส่วนแสดงสถิติหลัก ๆ -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon booking"><i class="fa-solid fa-calendar-check"></i></div>
                <div class="stat-number"><?php echo number_format($stats['total_bookings']); ?></div>
                <div class="stat-label">การจองทั้งหมด</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon income"><i class="fa-solid fa-money-bill-wave"></i></div>
                <div class="stat-number">฿<?php echo number_format($stats['total_income'], 2); ?></div>
                <div class="stat-label">รายได้รวม</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon customer"><i class="fa-solid fa-users"></i></div>
                <div class="stat-number"><?php echo number_format($stats['total_customers']); ?></div>
                <div class="stat-label">ลูกค้าทั้งหมด</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon room"><i class="fa-solid fa-bed"></i></div>
                <div class="stat-number"><?php echo number_format($stats['total_rooms']); ?></div>
                <div class="stat-label">ห้องพักทั้งหมด</div>
                </div>
            <div class="stat-card">
                <div class="stat-icon available"><i class="fa-solid fa-check-circle"></i></div>
                <div class="stat-number"><?php echo number_format($stats['available_rooms']); ?></div>
                <div class="stat-label">ห้องว่าง</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon occupied"><i class="fa-solid fa-times-circle"></i></div>
                <div class="stat-number"><?php echo number_format($stats['unavailable_rooms']); ?></div>
                <div class="stat-label">ห้องไม่ว่าง</div>
            </div>
        </div>
        <a href="statistics.php" class="stat-link">
            <i class="fa-solid fa-chart-line"></i> ดูรายงานสถิติการเข้าใช้ระบบ
        </a>
    </main>
</body>
</html>
