<?php
session_start();
$open_connect = 1;
require('connect.php');

if(!isset($_SESSION['id_account']) || !isset($_SESSION['role_account'])){
    die(header('Location: form-login.php'));
}elseif(isset($_GET['logout'])){
    session_destroy();
    die(header('Location: form-login.php'));
}else{
    $id_account = $_SESSION['id_account'];
    $query_show = "SELECT * FROM account WHERE id_account = '$id_account'";
    $call_back_show = mysqli_query($connect, $query_show);
    $result_show = mysqli_fetch_assoc($call_back_show);
    $directory = 'images_account/';
    $image_name = $directory . $result_show['images_account'];
}

$currentPage = 'booking';

$rooms = [
    ['id' => 101, 'name' => 'ห้องพัดลม', 'type' => 'ห้องพัดลม', 'price' => 5000, 'status' => 'ไม่ว่าง', 'image_url' => 'https://images.unsplash.com/photo-*************-42a792e24d32?q=80&w=1470&auto=format&fit=crop'],
    ['id' => 102, 'name' => 'ห้องแอร์วิวสวน', 'type' => 'ห้องแอร์', 'price' => 7500, 'status' => 'ว่าง', 'image_url' => 'https://images.unsplash.com/photo-*************-501ba68a0ba6?q=80&w=1470&auto=format&fit=crop'],
    ['id' => 201, 'name' => 'ห้องแอร์วิวทะเล', 'type' => 'ห้องแอร์', 'price' => 9000, 'status' => 'ว่าง', 'image_url' => 'https://images.unsplash.com/photo-*************-6a8506099945?q=80&w=1470&auto=format&fit=crop'],
    ['id' => 202, 'name' => 'ห้องสูท', 'type' => 'ห้องสูท', 'price' => 12000, 'status' => 'ไม่ว่าง', 'image_url' => 'https://images.unsplash.com/photo-1631049307264-da0ec9d70304?q=80&w=1470&auto=format&fit=crop'],
];

$room_types = array_unique(array_column($rooms, 'type'));
$selected_type = isset($_GET['type']) ? $_GET['type'] : 'ทั้งหมด';
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - ระบบจัดการห้องพัก</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Sarabun:wght@400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <aside class="sidebar">
        <div class="sidebar-header">
            <a href="#" class="logo">
                <i class="fa-solid fa-square-plus"></i>
                <span>ระบบการจัดการรีสอท์ร</span>
            </a>
        </div>
        <div class="profile-section">
            <div class="avatar-wrapper">
                <div class="avatar">RY</div>
                <div class="online-indicator"></div>
            </div>
            <div class="profile-info">
                <h4>100</h4>
                <p>ยินดีต้อนรับ</p>
            </div>
        </div>
        <ul class="sidebar-nav">
            <li class="nav-header">ห้องพัก</li>
            
            <li class="<?php if ($currentPage == 'booking') echo 'active'; ?>">
                <a href="index.php"><i class="fa-solid fa-circle-plus"></i><span>จองห้องพัก</span></a>
            </li>
            
            <li class="<?php if ($currentPage == 'edit_room') echo 'active'; ?>">
                <a href="roomedit.php"><i class="fa-solid fa-pen-to-square"></i><span>แก้ไขห้องพัก</span></a>
            </li>
           
            <li class="<?php if ($currentPage == 'history') echo 'active'; ?>">
                <a href="#"><i class="fa-solid fa-clock-rotate-left"></i><span>ประวัติการจอง</span></a>
            </li>
            
            <li class="<?php if ($currentPage == 'approve_booking') echo 'active'; ?>">
                <a href="approve-booking.php"><i class="fa-solid fa-check-to-slot"></i><span>อนุมัติการจอง</span></a>
            </li>
            
            <li class="<?php if ($currentPage == 'return_room') echo 'active'; ?>">
                <a href="return-room.php"><i class="fa-solid fa-right-left"></i><span>คืนห้องพัก</span></a>
            </li>

            <!-- ========== เพิ่มเมนูเครื่องดื่ม ที่นี่ ========== -->
            <li class="nav-header">เครื่องดื่ม</li>
            <li class="<?php if ($currentPage == 'beverages') echo 'active'; ?>">
                <a href="beverages.php"><i class="fa-solid fa-martini-glass-citrus"></i><span>เครื่องดื่ม</span></a>
            </li>
            <li class="<?php if ($currentPage == 'edit_beverages') echo 'active'; ?>">
                <a href="edit-beverages.php"><i class="fa-solid fa-pen-to-square"></i><span>แก้ไขเครื่องดื่ม</span></a>
            </li>

            <li class="nav-header">เกี่ยวกับฉัน</li>
            <li class="<?php if ($currentPage == 'profile') echo 'active'; ?>">
                <a href="#"><i class="fa-solid fa-user"></i><span>โปรไฟล์</span></a>
            </li>
            <li class="<?php if ($currentPage == 'password') echo 'active'; ?>">
                <a href="#"><i class="fa-solid fa-key"></i><span>เปลี่ยนรหัสผ่าน</span></a>
            </li>
            <li class="<?php if ($currentPage == 'logout') echo 'active'; ?>">
                <a href="form-login.php"><i class="fa-solid fa-right-from-bracket"></i><span>ออกจากระบบ</span></a>
            </li>
        </ul>
    </aside>

    
    <style>
        body { font-family: 'Sarabun', sans-serif; margin: 0; background-color: #f4f7f9; }
        .main-content { margin-left: 280px; padding: 20px; }
        .sidebar { 
            width: 280px; 
            height: 100vh;
            position: fixed; 
            top: 0; 
            left: 0; 
            background-color: #ffffff; 
            padding-top: 20px; 
            display: flex; 
            flex-direction: column;
            border-right: 1px solid #e7e7e7; 
        }
        .sidebar-header { padding: 0 25px; margin-bottom: 25px; height: 50px; display: flex; align-items: center; }
        .logo { display: flex; align-items: center; font-size: 24px; font-weight: 700; color: #343a40; text-decoration: none; }
        .logo i { color: #6d4aff; font-size: 32px; margin-right: 10px; }
        .profile-section { text-align: center; padding: 20px 0; border-bottom: 1px solid #e7e7e7; margin-bottom: 15px; }
        .avatar-wrapper { position: relative; display: inline-block; margin-bottom: 15px; }
        .avatar { width: 90px; height: 90px; background-color: #e85a4f; border-radius: 22%; display: flex; align-items: center; justify-content: center; color: white; font-size: 36px; font-weight: 700; box-shadow: 0 4px 15px rgba(232, 90, 79, 0.4); }
        .online-indicator { width: 12px; height: 12px; background-color: #2ecc71; border-radius: 50%; border: 2px solid white; position: absolute; top: 2px; right: 2px; }
        .profile-info h4 { margin: 0 0 5px 0; font-size: 20px; font-weight: 700; }
        .profile-info p { margin: 0; color: #6c757d; font-size: 16px; }
        .sidebar-nav {
            list-style: none;
            padding: 0;
            margin: 0;
            flex-grow: 1;
            overflow-y: auto;
            padding-bottom: 20px;
        }
        .nav-header { padding: 15px 25px 5px; font-size: 12px; color: #868e96; }
        .sidebar-nav li a { display: flex; align-items: center; padding: 14px 25px; color: #495057; text-decoration: none; font-size: 16px; font-weight: 500; position: relative; transition: all 0.2s ease-in-out; }
        .sidebar-nav li a:hover { background-color: #f1f3f5; }
        .sidebar-nav li.active a { background-color: #e7efff; color: #0d6efd; font-weight: 700;}
        .sidebar-nav li.active a::before { content: ''; position: absolute; left: 0; top: 0; height: 100%; width: 4px; background-color: #0d6efd; }
        .sidebar-nav li a i { width: 20px; margin-right: 15px; text-align: center; color: #868e96; }
        .sidebar-nav li.active a i { color: #0d6efd; }
        .filter-bar { background-color: #ffffff; padding: 15px 20px; border: 1px solid #e0e0e0; border-radius: 8px; margin-bottom: 25px; box-shadow: 0 2px 4px rgba(0,0,0,0.05); }
        .filter-bar label { font-weight: 700; margin-right: 10px; }
        .filter-bar select { padding: 8px 12px; border: 1px solid #ccc; border-radius: 4px; font-family: 'Sarabun', sans-serif; font-size: 16px; }
        .room-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); gap: 20px; }
        .room-card { background-color: #ffffff; border: 1px solid #e0e0e0; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 8px rgba(0,0,0,0.05); transition: transform 0.2s; }
        .room-card:hover { transform: translateY(-5px); }
        .room-card img { width: 100%; height: 180px; object-fit: cover; display: block; }
        .room-card-content { padding: 15px; }
        .room-card h3 { margin-top: 0; margin-bottom: 10px; font-size: 1.1em; }
        .room-card p { margin: 5px 0; color: #555; font-size: 0.95em; }
        .status { display: inline-block; padding: 4px 10px; border-radius: 12px; color: #fff; font-size: 0.85em; font-weight: 700; }
        .status-available { background-color: #28a745; }
        .status-unavailable { background-color: #dc3545; }
        .card-actions { margin-top: 15px; display: flex; gap: 10px; }
        .btn { padding: 8px 16px; border: none; border-radius: 4px; font-family: 'Sarabun', sans-serif; font-size: 15px; cursor: pointer; text-decoration: none; text-align: center; }
        .btn-details { background-color: #0d6efd; color: white; }
        .btn-edit { background-color: #65e354ff; color: #212529; border: 1px solid #28ee25ff; }
    </style>
</body>
</html>

