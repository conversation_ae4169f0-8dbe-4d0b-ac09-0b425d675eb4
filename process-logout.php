<?php
require('connect.php');
session_start();

if (isset($_SESSION['id_account'])) {
    $user_id = $_SESSION['id_account'];
    // อัปเดตสถานะเป็น 'ไม่ว่าง'
    $stmt = $connect->prepare("UPDATE account SET status_account = 'ไม่ว่าง' WHERE id_account = ?");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
}

// ทำลาย session
session_unset();
session_destroy();

// redirect ไปหน้า login
header('Location: form-login.php');
exit;
?>
