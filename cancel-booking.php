<?php
session_start();
require('connect.php');

// ตั้งค่า header สำหรับ JSON response
header('Content-Type: application/json');

// ตรวจสอบการล็อกอิน
if (!isset($_SESSION['id_account'])) {
    echo json_encode(['status' => 'error', 'message' => 'กรุณาเข้าสู่ระบบ']);
    exit;
}

// รับข้อมูล JSON
$input = json_decode(file_get_contents('php://input'), true);
$booking_id = isset($input['booking_id']) ? (int)$input['booking_id'] : 0;

if ($booking_id <= 0) {
    echo json_encode(['status' => 'error', 'message' => 'ข้อมูลไม่ถูกต้อง']);
    exit;
}

// ดึงข้อมูลการจอง
$query = "SELECT * FROM bookings WHERE id = ? AND status = 'รออนุมัติ'";
$stmt = $connect->prepare($query);
$stmt->bind_param('i', $booking_id);
$stmt->execute();
$result = $stmt->get_result();
$booking = $result->fetch_assoc();

if (!$booking) {
    echo json_encode(['status' => 'error', 'message' => 'ไม่พบการจองหรือไม่สามารถยกเลิกได้']);
    exit;
}

// อัปเดตสถานะเป็นยกเลิก
$update_query = "UPDATE bookings SET status = 'ยกเลิก' WHERE id = ?";
$update_stmt = $connect->prepare($update_query);
$update_stmt->bind_param('i', $booking_id);

if ($update_stmt->execute()) {
    echo json_encode(['status' => 'success', 'message' => 'ยกเลิกการจองสำเร็จ']);
} else {
    echo json_encode(['status' => 'error', 'message' => 'เกิดข้อผิดพลาดในการยกเลิก']);
}

$update_stmt->close();
$connect->close();
?>
