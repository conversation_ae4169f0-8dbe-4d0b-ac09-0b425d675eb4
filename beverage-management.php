<?php
session_start();
include('connect.php'); // สมมติว่ามีไฟล์นี้เชื่อมต่อฐานข้อมูล

// เพิ่มเครื่องดื่ม
if (isset($_POST['add_drink'])) {
    $name = mysqli_real_escape_string($conn, $_POST['drink_name']);
    $price = floatval($_POST['drink_price']);
    $sql = "INSERT INTO drinks (name, price) VALUES ('$name', $price)";
    mysqli_query($conn, $sql);
    header("Location: Manage-drinks.php");
    exit();
}

// ลบเครื่องดื่ม
if (isset($_GET['delete'])) {
    $id = intval($_GET['delete']);
    mysqli_query($conn, "DELETE FROM drinks WHERE id=$id");
    header("Location: Manage-drinks.php");
    exit();
}

// ดึงข้อมูลเครื่องดื่ม
$drinks = mysqli_query($conn, "SELECT * FROM drinks ORDER BY id DESC");
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <title>จัดการเมนูเครื่องดื่ม</title>
    <link rel="stylesheet" href="style.css"> <!-- สมมติใช้ style เดียวกับ employee.php -->
</head>
<body>
    <div class="container">
        <h2>จัดการเมนูเครื่องดื่ม</h2>
        <form method="post" class="form-inline">
            <input type="text" name="drink_name" placeholder="ชื่อเครื่องดื่ม" required>
            <input type="number" name="drink_price" placeholder="ราคา" step="0.01" required>
            <button type="submit" name="add_drink">เพิ่มเครื่องดื่ม</button>
        </form>
        <table>
            <thead>
                <tr>
                    <th>ชื่อเครื่องดื่ม</th>
                    <th>ราคา</th>
                    <th>จัดการ</th>
                </tr>
            </thead>
            <tbody>
                <?php while ($row = mysqli_fetch_assoc($drinks)): ?>
                <tr>
                    <td><?php echo htmlspecialchars($row['name']); ?></td>
                    <td><?php echo number_format($row['price'], 2); ?> บาท</td>
                    <td>
                        <a href="?delete=<?php echo $row['id']; ?>" onclick="return confirm('ยืนยันการลบ?')">ลบ</a>
                    </td>
                </tr>
                <?php endwhile; ?>
            </tbody>
        </table>
    </div>
</body>
</html>