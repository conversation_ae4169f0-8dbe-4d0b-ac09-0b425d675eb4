<?php
require('connect.php');

// ค้นหา
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$sql = "SELECT * FROM account";
if ($search !== '') {
    $sql .= " WHERE username_account LIKE ? OR email_account LIKE ?";
    $params = ["%$search%", "%$search%"];
    $stmt = $connect->prepare($sql);
    $stmt->bind_param("ss", ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
} else {
    $result = $connect->query($sql);
}
$users = $result->fetch_all(MYSQLI_ASSOC);
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <title>ข้อมูลยูสเซอร์</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #7b61ff; --primary-light: #f0edff; --primary-dark: #5c48d3;
            --secondary-color: #f8f9fa; --border-color-light: #e9ecef;
            --text-color: #333; --text-secondary: #6c757d; --bg-color: #f7f7fc;
            --sidebar-bg: #ffffff; --border-color: #e9e9f0; --sidebar-width: 260px;
            --green-color: #28a745; --red-color: #dc3545; --orange-color: #fd7e14;
        }
        body { font-family: 'Sarabun', sans-serif; margin: 0; background-color: var(--bg-color); }
        .main-content { margin-left: var(--sidebar-width); padding: 30px; }
        .sidebar { width: var(--sidebar-width); height: 100vh; position: fixed; top: 0; left: 0; background-color: var(--sidebar-bg); padding: 20px 15px; display: flex; flex-direction: column; border-right: 1px solid var(--border-color); box-sizing: border-box; overflow-y: auto; scrollbar-width: thin; scrollbar-color: #a084ff #f0edff; }
        .sidebar::-webkit-scrollbar { width: 10px; background: #f0edff; border-radius: 8px; }
        .sidebar::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #a084ff 0%, #7b61ff 100%);
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(123,97,255,0.15);
            border: 2px solid #f0edff;
            transition: background 0.3s;
        }
        .sidebar::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #7b61ff 0%, #5c48d3 100%);
        }
        .sidebar::-webkit-scrollbar-track {
            background: #f0edff;
            border-radius: 8px;
        }
        .sidebar-header { display: flex; align-items: center; padding: 0 10px 20px 10px; margin-bottom: 20px; border-bottom: 1px solid var(--border-color); }
        .logo-icon { font-size: 28px; color: var(--primary-color); margin-right: 12px; }
        .logo-text { font-size: 20px; font-weight: 700; color: var(--text-color); }
        .profile-section { display: flex; align-items: center; padding: 10px; margin-bottom: 20px; }
        .profile-avatar { width: 45px; height: 45px; min-width: 45px; background-color: var(--primary-light); color: var(--primary-dark); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 20px; margin-right: 15px; }
        .profile-info { line-height: 1.3; }
        .profile-name { font-weight: 600; margin: 0; color: var(--text-color); }
        .profile-role { font-size: 14px; color: var(--text-secondary); margin: 0; }
        .sidebar-nav { list-style: none; padding: 0; margin: 0; flex-grow: 1; }
        .nav-header { padding: 25px 10px 10px 10px; font-size: 13px; font-weight: 600; color: var(--text-secondary); text-transform: uppercase; }
        .sidebar-nav li a { display: flex; align-items: center; padding: 12px 15px; margin: 4px 0; color: var(--text-secondary); text-decoration: none; font-size: 15px; font-weight: 500; border-radius: 8px; }
        .sidebar-nav li a i { width: 22px; margin-right: 18px; font-size: 18px; text-align: center; }
        .sidebar-nav li:not(.active) a:hover { background-color: var(--primary-light); color: var(--primary-dark); }
        .sidebar-nav li.active a, .sidebar-nav li a[href='user.php'] { background-color: var(--primary-color); color: #ffffff; font-weight: 600; }
        .table thead th { background: #f8f9fa; }
        .status-active { background: #28a745; color: #fff; border-radius: 6px; padding: 2px 12px; font-size: 0.95em; }
        .action-btns .btn { margin-right: 4px; }
        .card { box-shadow: 0 2px 12px rgba(0,0,0,0.07); }
        .search-box { max-width: 220px; }
    </style>
</head>
<body>
    <aside class="sidebar">
        <div class="sidebar-header"> <i class="fa-solid fa-hotel logo-icon"></i> <span class="logo-text">Resort owner</span> </div>
        <div class="profile-section">
            <div class="profile-avatar"><i class="fa-solid fa-user-tie"></i></div>
            <div class="profile-info"><p class="profile-name">owneristrator</p><p class="profile-role">ผู้ดูแลระบบ</p></div>
        </div>
        <ul class="sidebar-nav">
            <li><a href="employee.php"><i class="fa-solid fa-user-plus"></i><span>ข้อมูลพนักงาน</span></a></li>
            <li><a href="user.php"><i class="fa-solid fa-id-card"></i><span>ข้อมูลยูสเซอร์</span></a></li>
            <li class="nav-header">ห้องพัก</li>
            <li><a href="?page=room"><i class="fa-solid fa-bed"></i><span>ห้องพัก</span></a></li>
            <li><a href="?page=edit_room"><i class="fa-solid fa-pen-ruler"></i><span>แก้ไขห้องพัก</span></a></li>
            <li class="nav-header">จองห้องพัก</li>
            <li><a href="?page=booking"><i class="fa-solid fa-calendar-plus"></i><span>จองห้องพัก</span></a></li>
            <li><a href="?page=approve_booking"><i class="fa-solid fa-circle-check"></i><span>อนุมัติการจอง</span></a></li>
            <li><a href="?page=return_room"><i class="fa-solid fa-rotate-left"></i><span>คืนห้องพัก</span></a></li>
            <li><a href="?page=history"><i class="fa-solid fa-clock-rotate-left"></i><span>ประวัติการจอง</span></a></li>
            <li class="nav-header">เครื่องดื่ม</li>
            <li><a href="#"><i class="fa-solid fa-mug-hot"></i><span>เครื่องดื่ม</span></a></li>
            <li><a href="#"><i class="fa-solid fa-pen-to-square"></i><span>แก้ไขเครื่องดื่ม</span></a></li>
            <li class="nav-header">เกี่ยวกับระบบ</li>
            <li><a href="#"><i class="fa-solid fa-key"></i><span>เปลี่ยนรหัสผ่าน</span></a></li>
            <li><a href="form-login.php?logout=1"><i class="fa-solid fa-arrow-right-from-bracket"></i><span>ออกจากระบบ</span></a></li>
        </ul>
    </aside>
    <main class="main-content">
        <h2 class="mb-4"><i class="fa-solid fa-id-card"></i> ข้อมูลยูสเซอร์</h2>
        <div class="card p-4">
            <div class="d-flex justify-content-between align-items-center mb-3 flex-wrap">
                <form class="d-flex mb-2 mb-md-0" method="get" action="">
                    <input type="text" class="form-control search-box" name="search" placeholder="ค้นหา..." value="<?=htmlspecialchars($search)?>">
                    <button class="btn btn-outline-secondary ms-2" type="submit"><i class="fa fa-search"></i></button>
                </form>
            </div>
            <div class="table-responsive">
                <table class="table table-hover align-middle">
                    <thead>
                        <tr>
                            <th>ลำดับ</th>
                            <th>ชื่อผู้ใช้</th>
                            <th>อีเมล</th>
                            <th>บทบาท</th>
                            <th>สถานะ</th>
                        </tr>
                    </thead>
                    <tbody>
                    <?php if (count($users) == 0): ?>
                        <tr><td colspan="6" class="text-center text-muted">ไม่พบข้อมูล</td></tr>
                    <?php else: foreach ($users as $i => $user): ?>
                        <tr>
                            <td><?= $i+1 ?></td>
                            <td><?= htmlspecialchars($user['username_account']) ?></td>
                            <td><?= htmlspecialchars($user['email_account']) ?></td>
                            <td><?= htmlspecialchars($user['role_account']) ?></td>
                            <td>
                              <?php
                                if ($user['status_account'] == 'ว่าง') {
                                    echo '<span class="badge bg-success">ว่าง</span>';
                                } else {
                                    echo '<span class="badge bg-danger">ไม่ว่าง</span>';
                                }
                              ?>
                            </td>
                        </tr>
                    <?php endforeach; endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </main>
</body>
</html>