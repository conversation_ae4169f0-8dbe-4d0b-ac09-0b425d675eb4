<?php
require('connect.php');

// ตรวจสอบว่าเป็นการแก้ไขหรือเพิ่มใหม่
$is_edit = false;
$employee = [
    'id' => '',
    'username' => '',
    'fullname' => '',
    'phone' => '',
    'status' => 'ใช้งาน'
];

if (isset($_GET['id'])) {
    $is_edit = true;
    $id = (int)$_GET['id'];
    $stmt = $connect->prepare("DELETE FROM employees WHERE id=?");
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();
    $employee = $result->fetch_assoc();
    if (!$employee) {
        header('Location: employee.php');
        exit;
    }
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <title><?= $is_edit ? 'แก้ไข' : 'เพิ่ม' ?>พนักงาน</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" rel="stylesheet">
    <style>
        body { background: #f7f7fc; }
        .form-container { max-width: 500px; margin: 40px auto; background: #fff; border-radius: 12px; box-shadow: 0 2px 12px rgba(0,0,0,0.07); padding: 32px; }
        .form-title { font-weight: 700; margin-bottom: 24px; }
    </style>
</head>
<body>
<div class="form-container">
    <h3 class="form-title">
        <i class="fa-solid fa-user-plus"></i>
        <?= $is_edit ? 'แก้ไขข้อมูลพนักงาน' : 'เพิ่มพนักงานใหม่' ?>
    </h3>
    <form method="post" action="process-employee.php">
        <?php if ($is_edit): ?>
            <input type="hidden" name="action" value="edit">
            <input type="hidden" name="id" value="<?= htmlspecialchars($employee['id']) ?>">
        <?php else: ?>
            <input type="hidden" name="action" value="add">
        <?php endif; ?>
        <div class="mb-3">
            <label class="form-label">ชื่อในการเข้าสู่ระบบ <span class="text-danger">*</span></label>
            <input type="text" name="username" class="form-control" required value="<?= htmlspecialchars($employee['username']) ?>">
        </div>
        <div class="mb-3">
            <label class="form-label">ชื่อ-นามสกุล <span class="text-danger">*</span></label>
            <input type="text" name="fullname" class="form-control" required value="<?= htmlspecialchars($employee['fullname']) ?>">
        </div>
        <div class="mb-3">
            <label class="form-label">เบอร์โทรศัพท์</label>
            <input type="text" name="phone" class="form-control" value="<?= htmlspecialchars($employee['phone']) ?>">
        </div>
        <div class="mb-3">
            <label class="form-label">สถานะ</label>
            <select name="status" class="form-select">
                <option value="ใช้งาน" <?= $employee['status']=='ใช้งาน'?'selected':'' ?>>ใช้งาน</option>
                <option value="ไม่ใช้งาน" <?= $employee['status']=='ไม่ใช้งาน'?'selected':'' ?>>ไม่ใช้งาน</option>
            </select>
        </div>
        <div class="d-flex justify-content-between">
            <a href="employee.php" class="btn btn-secondary"><i class="fa fa-arrow-left"></i> กลับ</a>
            <button type="submit" class="btn btn-primary">
                <i class="fa fa-save"></i> <?= $is_edit ? 'บันทึกการแก้ไข' : 'เพิ่มพนักงาน' ?>
            </button>
        </div>
    </form>
</div>
</body>
</html>
