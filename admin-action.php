<?php
// ... (โค้ดส่วน login check ของแอดมิน) ...
include 'connect.php';

if (isset($_GET['id']) && isset($_GET['action'])) {
    $booking_id = (int)$_GET['id'];
    $action = $_GET['action'];

    $new_status = '';
    if ($action == 'approve') {
        $new_status = 'approved';
    } elseif ($action == 'reject') {
        $new_status = 'rejected';
    }

    if (!empty($new_status)) {
        // อัปเดตสถานะใน Database
        $sql = "UPDATE bookings SET status = ? WHERE id = ?";
        $stmt = $connect->prepare($sql); // เปลี่ยน $conn เป็น $connect
        $stmt->bind_param("si", $new_status, $booking_id);
        $stmt->execute();
        $stmt->close();
    }
}

// เมื่อทำงานเสร็จ ให้ redirect กลับไปหน้า dashboard
header('Location: admin-dashboard.php');
exit;
?>