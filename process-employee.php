<?php
require('connect.php');

// เพิ่มพนักงาน
if (isset($_POST['action']) && $_POST['action'] === 'add') {
    $username = trim($_POST['username']);
    $fullname = trim($_POST['fullname']);
    $phone = trim($_POST['phone']);
    $status = isset($_POST['status']) ? $_POST['status'] : 'ใช้งาน';

    $stmt = $connect->prepare("INSERT INTO employees (username, fullname, phone, status) VALUES (?, ?, ?, ?)");
    $stmt->bind_param("ssss", $username, $fullname, $phone, $status);
    $stmt->execute();
    header('Location: employee.php');
    exit;
}

// แก้ไขพนักงาน
if (isset($_POST['action']) && $_POST['action'] === 'edit' && isset($_POST['id'])) {
    $id = (int)$_POST['id'];
    $username = trim($_POST['username']);
    $fullname = trim($_POST['fullname']);
    $phone = trim($_POST['phone']);
    $status = isset($_POST['status']) ? $_POST['status'] : 'ใช้งาน';

    $stmt = $connect->prepare("UPDATE employees SET username=?, fullname=?, phone=?, status=? WHERE id=?");
    $stmt->bind_param("ssssi", $username, $fullname, $phone, $status, $id);
    $stmt->execute();
    header('Location: employee.php');
    exit;
}

// ลบพนักงาน
if (isset($_GET['action']) && $_GET['action'] === 'delete' && isset($_GET['id'])) {
    $id = (int)$_GET['id'];
    $stmt = $connect->prepare("DELETE FROM employees WHERE id=?");
    $stmt->bind_param("i", $id);
    $stmt->execute();
    header('Location: employee.php');
    exit;
}

// ถ้าไม่มี action ให้ redirect กลับ
header('Location: employee.php');
exit;
