<?php
// history.php

session_start();
require('connect.php');

// --- ส่วนของการจำลองว่ามีลูกค้าล็อกอินอยู่ ---
if (!isset($_SESSION['customer_phone'])) {
    // ในระบบจริง ค่านี้ควรมาจากหน้า Login
    // ใส่เบอร์โทรของลูกค้าที่มีข้อมูลการจองในตาราง bookings เพื่อทดสอบ
    $_SESSION['customer_phone'] = '0812345678'; 
}

// -----------------------------------------------------------
// !! แก้ไขตรงนี้ !!
// เรียกใช้ไฟล์ connect.php ที่มีอยู่ในโปรเจกต์ของคุณ
// -----------------------------------------------------------

// ดึงเบอร์โทรของลูกค้าที่ล็อกอินอยู่จาก Session
$current_customer_phone = $_SESSION['customer_phone'];

// เตรียมคำสั่ง SQL 
// **กรุณาตรวจสอบชื่อคอลัมน์ในตาราง 'room' ของคุณ หากไม่ใช่ 'name' ให้แก้ไข**
$sql = "SELECT 
            b.id AS booking_id,
            b.customer_name,
            b.check_in_date,
            b.check_out_date,
            b.guests,
            b.total_price,
            b.deposit_amount,
            b.status,
            b.created_at AS booking_date,
            r.name AS room_name  -- <-- ถ้าชื่อคอลัมน์ในตาราง room คือ room_name ให้แก้เป็น r.room_name
        FROM bookings AS b
        JOIN room AS r ON b.room_id = r.id
        WHERE b.customer_phone = ?
        ORDER BY b.created_at DESC";

// ใช้ Prepared Statement เพื่อความปลอดภัย
$stmt = $conn->prepare($sql);
$stmt->bind_param("s", $current_customer_phone);
$stmt->execute();
$result = $stmt->get_result();

$bookings = $result->fetch_all(MYSQLI_ASSOC);
$customer_display_name = !empty($bookings) ? $bookings[0]['customer_name'] : 'ลูกค้า';

?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ประวัติการจอง</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Sarabun:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"/>
    <style>
        body { font-family: 'Sarabun', sans-serif; background-color: #f8f9fa; }
        .booking-card { border: 1px solid #e0e0e0; border-left: 5px solid #6c757d; transition: all 0.3s ease-in-out; }
        .booking-card:hover { transform: translateY(-5px); box-shadow: 0 4px 15px rgba(0,0,0,0.1); }
        .status-badge { font-size: 0.9em; color: white; }
    </style>
</head>
<body>

    <div class="container mt-5 mb-5">
        <div class="d-flex align-items-center mb-4">
            <i class="fa-solid fa-clock-rotate-left fa-2x me-3 text-secondary"></i>
            <h1 class="mb-0">ประวัติการจอง</h1>
        </div>
        
        <p class="lead">สวัสดีคุณ <?php echo htmlspecialchars($customer_display_name); ?>, นี่คือรายการจองทั้งหมดของคุณ</p>
        <hr>

        <?php if (!empty($bookings)): ?>
            <?php foreach($bookings as $booking): ?>
                <?php
                    // กำหนดสีของ card และ badge ตามสถานะ
                    $card_border_color = 'secondary';
                    $badge_bg_color = 'bg-secondary';
                    switch($booking['status']) {
                        case 'อนุมัติแล้ว': $card_border_color = 'primary'; $badge_bg_color = 'bg-primary'; break;
                        case 'เสร็จสิ้น': $card_border_color = 'success'; $badge_bg_color = 'bg-success'; break;
                        case 'ปฏิเสธ': $card_border_color = 'danger'; $badge_bg_color = 'bg-danger'; break;
                        case 'รออนุมัติ': $card_border_color = 'warning'; $badge_bg_color = 'bg-warning text-dark'; break;
                    }
                ?>
                <div class="card booking-card mb-3" style="border-left-color: var(--bs-<?php echo $card_border_color; ?>);">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-7">
                                <h5 class="card-title"><?php echo htmlspecialchars($booking['room_name'] ?? 'ไม่มีชื่อห้อง'); ?></h5>
                                <p class="card-text mb-1"><i class="fa-solid fa-calendar-check text-success"></i> <strong>เช็คอิน:</strong> <?php echo date("d/m/Y", strtotime($booking['check_in_date'])); ?></p>
                                <p class="card-text"><i class="fa-solid fa-calendar-times text-danger"></i> <strong>เช็คเอาท์:</strong> <?php echo date("d/m/Y", strtotime($booking['check_out_date'])); ?></p>
                                <p class="card-text"><i class="fa-solid fa-users text-info"></i> <strong>จำนวนผู้เข้าพัก:</strong> <?php echo htmlspecialchars($booking['guests']); ?> คน</p>
                            </div>
                            <div class="col-md-5 text-md-end">
                                <p class="mb-2"><strong>สถานะ:</strong> <span class="badge rounded-pill <?php echo $badge_bg_color; ?> status-badge"><?php echo htmlspecialchars($booking['status']); ?></span></p>
                                <p class="mb-1"><strong>ยอดรวม:</strong> <?php echo number_format($booking['total_price'], 2); ?> บาท</p>
                                <p class="mb-2"><strong>ยอดมัดจำ:</strong> <?php echo number_format($booking['deposit_amount'], 2); ?> บาท</p>
                                <small class="text-muted">จองเมื่อ: <?php echo date("d/m/Y H:i", strtotime($booking['booking_date'])); ?></small>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php else: ?>
            <div class="alert alert-info" role="alert"><i class="fa-solid fa-info-circle"></i> คุณยังไม่มีประวัติการจอง</div>
        <?php endif; ?>
        
        <?php
            // ปิด statement และ connection
            $stmt->close();
            $conn->close();
        ?>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>