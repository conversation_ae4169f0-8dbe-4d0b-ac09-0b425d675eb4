<?php
session_start();
require('connect.php');

// ตรวจสอบการล็อกอินและสิทธิ์พนักงาน
if (!isset($_SESSION['id_account']) || !isset($_SESSION['role_account'])) {
    header('Location: form-login.php');
    exit;
}

$role_account = $_SESSION['role_account'];

// ตรวจสอบว่าเป็นพนักงานหรือไม่
if ($role_account !== 'employee' && $role_account !== 'admin' && $role_account !== 'owner') {
    header('Location: employee.php');
    exit;
}

$id_account = $_SESSION['id_account'];

// ดึงข้อมูลผู้ใช้
$query_show = "SELECT username_account, role_account, images_account FROM account WHERE id_account = '$id_account'";
$call_back_show = mysqli_query($connect, $query_show);
$result_show = mysqli_fetch_assoc($call_back_show);

// ฟิลเตอร์การค้นหา
$status_filter = isset($_GET['status']) ? $_GET['status'] : '';
$date_filter = isset($_GET['date']) ? $_GET['date'] : '';
$search_customer = isset($_GET['search']) ? $_GET['search'] : '';

// สร้าง query สำหรับดึงข้อมูลการจองทั้งหมด
$where_conditions = [];
$params = [];
$param_types = '';

if ($status_filter) {
    $where_conditions[] = "b.status = ?";
    $params[] = $status_filter;
    $param_types .= 's';
}

if ($date_filter) {
    $where_conditions[] = "DATE(b.created_at) = ?";
    $params[] = $date_filter;
    $param_types .= 's';
}

if ($search_customer) {
    $where_conditions[] = "(b.customer_name LIKE ? OR b.customer_phone LIKE ?)";
    $params[] = "%$search_customer%";
    $params[] = "%$search_customer%";
    $param_types .= 'ss';
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

$query_bookings = "SELECT b.*, r.room_number, r.room_type, r.price 
                   FROM bookings b 
                   JOIN room r ON b.room_id = r.id_room 
                   $where_clause
                   ORDER BY b.created_at DESC";

$stmt = $connect->prepare($query_bookings);

if (!empty($params)) {
    $stmt->bind_param($param_types, ...$params);
}

$stmt->execute();
$result_bookings = $stmt->get_result();

$bookings = [];
while ($row = $result_bookings->fetch_assoc()) {
    $bookings[] = $row;
}

// นับจำนวนการจองตามสถานะ
$stats_query = "SELECT 
                    status,
                    COUNT(*) as count
                 FROM bookings 
                 GROUP BY status";
$stats_result = mysqli_query($connect, $stats_query);
$stats = [];
while ($row = mysqli_fetch_assoc($stats_result)) {
    $stats[$row['status']] = $row['count'];
}

// ฟังก์ชันแปลงสถานะเป็นภาษาไทย
function getStatusThai($status) {
    $statuses = [
        'รออนุมัติ' => 'รออนุมัติ',
        'อนุมัติแล้ว' => 'อนุมัติแล้ว',
        'ปฏิเสธ' => 'ปฏิเสธ',
        'ยกเลิก' => 'ยกเลิก',
        'คืนห้องแล้ว' => 'คืนห้องแล้ว'
    ];
    return $statuses[$status] ?? $status;
}

// ฟังก์ชันแปลงสถานะเป็นสี
function getStatusColor($status) {
    $colors = [
        'รออนุมัติ' => 'warning',
        'อนุมัติแล้ว' => 'success',
        'ปฏิเสธ' => 'danger',
        'ยกเลิก' => 'secondary',
        'คืนห้องแล้ว' => 'info'
    ];
    return $colors[$status] ?? 'primary';
}

// ฟังก์ชันแปลงประเภทห้องเป็นภาษาไทย
function getRoomTypeThai($type) {
    $types = [
        'Standard' => 'ห้องมาตรฐาน',
        'Deluxe' => 'ห้องดีลักซ์',
        'Suite' => 'ห้องสวีท',
        'Family' => 'ห้องครอบครัว',
        'VIP' => 'ห้องวีไอพี'
    ];
    return $types[$type] ?? $type;
}

$currentPage = 'booking_history';
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ประวัติการจอง - ระบบจัดการรีสอร์ท</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Sarabun:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: #7b43ff;
            --light-primary-color: #f3f0ff;
            --text-color: #495057;
            --text-secondary-color: #6c757d;
            --sidebar-bg: #ffffff;
            --main-bg: #f9fafb;
            --border-color: #e7e7e7;
            --success-color: #28a745;
            --info-color: #17a2b8;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --secondary-color: #6c757d;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Sarabun', sans-serif;
            background: #f8fafc;
            color: var(--text-color);
            min-height: 100vh;
        }
        
        .main-content {
            margin-left: 280px; 
            padding: 0;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 280px;
            height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 20px;
            display: flex;
            flex-direction: column;
            box-sizing: border-box;
            border-right: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .sidebar-header {
            padding: 0 10px;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
        }
        
        .logo {
            display: flex;
            align-items: center;
            font-size: 22px;
            font-weight: 700;
            color: #343a40;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .logo:hover {
            color: var(--primary-color);
            transform: translateY(-2px);
        }
        
        .logo i {
            color: var(--primary-color);
            font-size: 30px;
            margin-right: 12px;
            transition: all 0.3s ease;
        }
        
        .user-profile {
            display: flex;
            align-items: center;
            padding: 15px 10px;
            margin-bottom: 15px;
            border-top: 1px solid var(--border-color);
            border-bottom: 1px solid var(--border-color);
            background: rgba(255, 255, 255, 0.5);
            border-radius: 12px;
        }
        
        .user-avatar {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), #9c6fff);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 22px;
            margin-right: 15px;
            box-shadow: 0 4px 15px rgba(123, 67, 255, 0.3);
        }
        
        .user-info h5 {
            margin: 0;
            font-weight: 700;
            font-size: 16px;
        }
        
        .user-info p {
            margin: 0;
            font-size: 14px;
            color: var(--text-secondary-color);
        }
        
        .sidebar-nav {
            list-style: none;
            padding: 0;
            margin: 0;
            flex-grow: 1; 
        }
        
        .nav-header {
            padding: 15px 15px 8px;
            font-size: 12px;
            font-weight: 700;
            color: #adb5bd;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .sidebar-nav li a { 
            display: flex; 
            align-items: center; 
            padding: 15px 20px; 
            margin: 2px 10px; 
            color: var(--text-color); 
            text-decoration: none; 
            font-size: 15px; 
            font-weight: 500; 
            border-radius: 10px; 
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .sidebar-nav li a::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 0;
            background: linear-gradient(90deg, var(--primary-color), #9c6fff);
            transition: width 0.3s ease;
            z-index: -1;
        }

        .sidebar-nav li a:hover::before {
            width: 100%;
        }

        .sidebar-nav li a i { 
            width: 24px; 
            margin-right: 15px; 
            text-align: center; 
            font-size: 18px; 
            color: var(--text-secondary-color); 
            transition: all 0.3s ease;
        }

        .sidebar-nav li a span {
            transition: all 0.3s ease;
        }

        .sidebar-nav li.active a { 
            background: linear-gradient(135deg, var(--primary-color), #9c6fff);
            color: #fff; 
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(123, 67, 255, 0.3);
            transform: translateX(5px);
        }

        .sidebar-nav li.active a i { 
            color: #fff;
            transform: scale(1.1);
        }

        .sidebar-nav li a:hover { 
            color: #fff;
            transform: translateX(5px);
        }

        .sidebar-nav li a:hover i { 
            color: #fff;
            transform: scale(1.1);
        }

        .sidebar-nav li.active a:hover { 
            background: linear-gradient(135deg, var(--primary-color), #9c6fff);
            color: #fff; 
            cursor: default;
            transform: translateX(5px);
        }
        
        .sidebar-nav .logout-item {
             margin-top: auto; 
        }
        
        .sidebar-nav .logout-item a {
            background: linear-gradient(135deg, #dc3545, #e74c3c);
            color: white;
            border-radius: 12px;
            margin: 0;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
        }
        
        .sidebar-nav .logout-item a:hover {
            background: linear-gradient(135deg, #c82333, #d63384);
            transform: translateX(5px);
        }

        /* Page Header */
        .page-header {
            background: white;
            padding: 40px 60px;
            border-bottom: 1px solid #e9ecef;
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
        }

        .page-title {
            font-size: 2.5rem;
            font-weight: 300;
            color: #2c3e50;
            margin-bottom: 10px;
            letter-spacing: -0.5px;
        }

        .page-subtitle {
            font-size: 1.1rem;
            color: #6c757d;
            font-weight: 400;
        }

        /* Stats Cards */
        .stats-container {
            padding: 40px 60px;
            background: white;
            border-bottom: 1px solid #e9ecef;
        }

        .stats-grid {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
            border: 1px solid #f1f3f4;
            text-align: center;
        }

        .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 20px;
            color: white;
        }

        .stat-icon.warning { background: var(--warning-color); }
        .stat-icon.success { background: var(--success-color); }
        .stat-icon.danger { background: var(--danger-color); }
        .stat-icon.secondary { background: var(--secondary-color); }
        .stat-icon.info { background: var(--info-color); }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
            font-weight: 500;
        }

        /* Filter Bar */
        .filter-container {
            background: white;
            padding: 30px 60px;
            border-bottom: 1px solid #e9ecef;
        }

        .filter-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            gap: 20px;
            align-items: center;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .filter-label {
            font-size: 0.9rem;
            font-weight: 500;
            color: #495057;
        }

        .filter-input {
            padding: 10px 15px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            font-size: 0.9rem;
            min-width: 200px;
        }

        .filter-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(123, 67, 255, 0.1);
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            text-align: center;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: #6a3ce7;
        }

        .btn-outline {
            background: transparent;
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
        }

        .btn-outline:hover {
            background: var(--primary-color);
            color: white;
        }

        /* History Container */
        .history-container {
            padding: 60px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .history-grid {
            display: grid;
            gap: 30px;
        }

        .booking-card {
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #f1f3f4;
            transition: all 0.3s ease;
        }

        .booking-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 40px rgba(0, 0, 0, 0.12);
        }

        .booking-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 1px solid #f1f3f4;
        }

        .booking-info {
            flex: 1;
        }

        .booking-number {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .booking-customer {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 5px;
        }

        .booking-date {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .status-badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            letter-spacing: 0.5px;
            text-transform: uppercase;
        }

        .status-warning { background: #fff3cd; color: #856404; }
        .status-success { background: #d4edda; color: #155724; }
        .status-danger { background: #f8d7da; color: #721c24; }
        .status-secondary { background: #e2e3e5; color: #383d41; }
        .status-info { background: #d1ecf1; color: #0c5460; }

        .booking-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }

        .detail-item {
            display: flex;
            flex-direction: column;
        }

        .detail-label {
            font-size: 0.8rem;
            color: #6c757d;
            font-weight: 500;
            margin-bottom: 5px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .detail-value {
            font-size: 1rem;
            color: #2c3e50;
            font-weight: 500;
        }

        .booking-actions {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }

        .btn-danger {
            background: var(--danger-color);
            color: white;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .btn-secondary {
            background: var(--secondary-color);
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        /* No Bookings */
        .no-bookings {
            text-align: center;
            padding: 80px 40px;
            background: white;
            border-radius: 16px;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
        }

        .no-bookings i {
            font-size: 4rem;
            color: #adb5bd;
            margin-bottom: 20px;
        }

        .no-bookings h2 {
            font-size: 1.8rem;
            color: #2c3e50;
            margin-bottom: 10px;
            font-weight: 400;
        }

        .no-bookings p {
            color: #6c757d;
            font-size: 1rem;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .page-header,
            .stats-container,
            .filter-container,
            .history-container {
                padding: 30px 20px;
            }
            
            .filter-content {
                flex-direction: column;
                align-items: stretch;
            }
            
            .booking-header {
                flex-direction: column;
                gap: 15px;
            }
            
            .booking-details {
                grid-template-columns: 1fr;
            }
            
            .booking-actions {
                flex-direction: column;
            }
        }

        /* Animations */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .booking-card {
            animation: fadeIn 0.6s ease forwards;
        }

        .booking-card:nth-child(1) { animation-delay: 0.1s; }
        .booking-card:nth-child(2) { animation-delay: 0.2s; }
        .booking-card:nth-child(3) { animation-delay: 0.3s; }
        .booking-card:nth-child(4) { animation-delay: 0.4s; }
        .booking-card:nth-child(5) { animation-delay: 0.5s; }
        .booking-card:nth-child(6) { animation-delay: 0.6s; }
    </style>
</head>
<body>
    
<aside class="sidebar">
    <div class="sidebar-header">
        <a href="employee.php" class="logo">
            <i class="fa-solid fa-hotel"></i>
            <span>ระบบจัดการรีสอร์ท</span>
        </a>
    </div>
    <div class="user-profile">
        <div class="user-avatar">
            <i class="fa-solid fa-user"></i>
        </div>
        <div class="user-info">
            <h5><?php echo htmlspecialchars($result_show['username_account']); ?></h5>
            <p><?php echo htmlspecialchars($result_show['role_account']); ?></p>
        </div>
    </div>
    <div class="sidebar-content">
        <ul class="sidebar-nav">
            <li class="nav-header">
                <i class="fa-solid fa-bed"></i>
                <span>ห้องพัก</span>
            </li>
            <li class="<?php if ($currentPage == 'dashboard') echo 'active'; ?>">
                <a href="employee.php"><i class="fa-solid fa-tachometer-alt"></i><span>จัดการห้องพัก</span></a>
            </li>
            
            <li class="<?php if ($currentPage == 'approve_booking') echo 'active'; ?>">
                <a href="booking-management.php"><i class="fa-solid fa-calendar-check"></i><span>อนุมัติการจอง</span></a>
            </li>
            <li class="<?php if ($currentPage == 'return_room') echo 'active'; ?>">
                <a href="return-the-room.php"><i class="fa-solid fa-key"></i><span>คืนห้องพัก</span></a>
            </li>
            <li class="<?php if ($currentPage == 'booking_history') echo 'active'; ?>">
                <a href="booking-history.php"><i class="fa-solid fa-history"></i><span>ประวัติการจอง</span></a>
            </li>
            
            <li class="nav-header">
                <i class="fa-solid fa-concierge-bell"></i>
                <span>บริการเสริม</span>
            </li>
            <li class="<?php if ($currentPage == 'beverage') echo 'active'; ?>">
                <a href="beverage-management.php"><i class="fa-solid fa-cocktail"></i><span>จัดการเครื่องดื่ม</span></a>
            </li>
            <li class="<?php if ($currentPage == 'bakery') echo 'active'; ?>">
                <a href="bakery-management.php"><i class="fa-solid fa-cookie-bite"></i><span>จัดขนมเบเกอรี่</span></a>
            </li>
            
            
            <li class="nav-header">
                <i class="fa-solid fa-cash-register"></i>
                <span>การเงิน</span>
            </li>
            <li class="<?php if ($currentPage == 'receipt') echo 'active'; ?>">
                <a href="receipt.php"><i class="fa-solid fa-file-invoice-dollar"></i><span>ออกใบเสร็จ</span></a>
            </li>
            
            <li class="nav-header">
                <i class="fa-solid fa-user-cog"></i>
                <span>บัญชีผู้ใช้</span>
            </li>
            <li class="<?php if ($currentPage == 'change_password') echo 'active'; ?>">
                <a href="change-password.php"><i class="fa-solid fa-lock"></i><span>เปลี่ยนรหัสผ่าน</span></a>
            </li>
            
            <li class="logout-item">
                <a href="form-login.php?logout=1"><i class="fa-solid fa-sign-out-alt"></i><span>ออกจากระบบ</span></a>
            </li>
        </ul>
    </div>
</aside>

    <div class="main-content">
        <!-- Page Header -->
        <header class="page-header">
            <div class="header-content">
                <h1 class="page-title">ประวัติการจองทั้งหมด</h1>
                <p class="page-subtitle">ดูประวัติการจองของลูกค้าทั้งหมด</p>
            </div>
        </header>

        <!-- Stats Cards -->
        <div class="stats-container">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon warning">
                        <i class="fa-solid fa-clock"></i>
                    </div>
                    <div class="stat-number"><?php echo $stats['รออนุมัติ'] ?? 0; ?></div>
                    <div class="stat-label">รออนุมัติ</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon success">
                        <i class="fa-solid fa-check"></i>
                    </div>
                    <div class="stat-number"><?php echo $stats['อนุมัติแล้ว'] ?? 0; ?></div>
                    <div class="stat-label">อนุมัติแล้ว</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon danger">
                        <i class="fa-solid fa-times"></i>
                    </div>
                    <div class="stat-number"><?php echo $stats['ปฏิเสธ'] ?? 0; ?></div>
                    <div class="stat-label">ปฏิเสธ</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon secondary">
                        <i class="fa-solid fa-ban"></i>
                    </div>
                    <div class="stat-number"><?php echo $stats['ยกเลิก'] ?? 0; ?></div>
                    <div class="stat-label">ยกเลิก</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon info">
                        <i class="fa-solid fa-undo"></i>
                    </div>
                    <div class="stat-number"><?php echo $stats['คืนห้องแล้ว'] ?? 0; ?></div>
                    <div class="stat-label">คืนห้องแล้ว</div>
                </div>
            </div>
        </div>

        <!-- Filter Bar -->
        <div class="filter-container">
            <div class="filter-content">
                <form method="GET" action="" style="display: flex; gap: 20px; align-items: end; flex-wrap: wrap;">
                    <div class="filter-group">
                        <label class="filter-label">ค้นหาลูกค้า</label>
                        <input type="text" name="search" class="filter-input" 
                               placeholder="ชื่อหรือเบอร์โทร" 
                               value="<?php echo htmlspecialchars($search_customer); ?>">
                    </div>
                    
                    <div class="filter-group">
                        <label class="filter-label">สถานะ</label>
                        <select name="status" class="filter-input">
                            <option value="">ทั้งหมด</option>
                            <option value="รออนุมัติ" <?php echo $status_filter == 'รออนุมัติ' ? 'selected' : ''; ?>>รออนุมัติ</option>
                            <option value="อนุมัติแล้ว" <?php echo $status_filter == 'อนุมัติแล้ว' ? 'selected' : ''; ?>>อนุมัติแล้ว</option>
                            <option value="ปฏิเสธ" <?php echo $status_filter == 'ปฏิเสธ' ? 'selected' : ''; ?>>ปฏิเสธ</option>
                            <option value="ยกเลิก" <?php echo $status_filter == 'ยกเลิก' ? 'selected' : ''; ?>>ยกเลิก</option>
                            <option value="คืนห้องแล้ว" <?php echo $status_filter == 'คืนห้องแล้ว' ? 'selected' : ''; ?>>คืนห้องแล้ว</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label class="filter-label">วันที่จอง</label>
                        <input type="date" name="date" class="filter-input" 
                               value="<?php echo htmlspecialchars($date_filter); ?>">
                    </div>
                    
                    <div class="filter-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="fa-solid fa-search"></i> ค้นหา
                        </button>
                    </div>
                    
                    <div class="filter-group">
                        <a href="booking-history.php" class="btn btn-outline">
                            <i class="fa-solid fa-refresh"></i> รีเซ็ต
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- History Container -->
        <div class="history-container">
            <div class="history-grid">
                <?php if (!empty($bookings)): ?>
                    <?php foreach ($bookings as $booking): ?>
                        <div class="booking-card">
                            <div class="booking-header">
                                <div class="booking-info">
                                    <div class="booking-number">ห้อง <?php echo htmlspecialchars($booking['room_number']); ?></div>
                                    <div class="booking-customer">
                                        <strong>ลูกค้า:</strong> <?php echo htmlspecialchars($booking['customer_name']); ?> 
                                        (<?php echo htmlspecialchars($booking['customer_phone']); ?>)
                                    </div>
                                    <div class="booking-date">
                                        จองเมื่อ: <?php echo date('d/m/Y H:i', strtotime($booking['created_at'])); ?>
                                    </div>
                                </div>
                                <div class="status-badge status-<?php echo getStatusColor($booking['status']); ?>">
                                    <?php echo getStatusThai($booking['status']); ?>
                                </div>
                            </div>
                            
                            <div class="booking-details">
                                <div class="detail-item">
                                    <div class="detail-label">ประเภทห้อง</div>
                                    <div class="detail-value"><?php echo getRoomTypeThai($booking['room_type']); ?></div>
                                </div>
                                
                                <div class="detail-item">
                                    <div class="detail-label">วันที่เช็คอิน</div>
                                    <div class="detail-value"><?php echo date('d/m/Y', strtotime($booking['check_in_date'])); ?></div>
                                </div>
                                
                                <div class="detail-item">
                                    <div class="detail-label">วันที่เช็คเอาท์</div>
                                    <div class="detail-value"><?php echo date('d/m/Y', strtotime($booking['check_out_date'])); ?></div>
                                </div>
                                
                                <div class="detail-item">
                                    <div class="detail-label">จำนวนคน</div>
                                    <div class="detail-value"><?php echo $booking['guests']; ?> คน</div>
                                </div>
                                
                                <div class="detail-item">
                                    <div class="detail-label">ราคารวม</div>
                                    <div class="detail-value">฿<?php echo number_format($booking['total_price']); ?></div>
                                </div>
                                
                                <div class="detail-item">
                                    <div class="detail-label">มัดจำ</div>
                                    <div class="detail-value">฿<?php echo number_format($booking['deposit_amount']); ?></div>
                                </div>
                            </div>
                            
                            <div class="booking-actions">
                                <?php if ($booking['status'] == 'รออนุมัติ'): ?>
                                    <a href="booking-management.php?action=approve&booking_id=<?php echo $booking['id']; ?>" 
                                       class="btn btn-primary"
                                       onclick="return confirm('คุณต้องการอนุมัติการจองนี้หรือไม่?')">
                                        <i class="fa-solid fa-check"></i> อนุมัติ
                                    </a>
                                    <a href="booking-management.php?action=reject&booking_id=<?php echo $booking['id']; ?>" 
                                       class="btn btn-danger"
                                       onclick="return confirm('คุณต้องการปฏิเสธการจองนี้หรือไม่?')">
                                        <i class="fa-solid fa-times"></i> ปฏิเสธ
                                    </a>
                                <?php endif; ?>
                                
                                <?php if ($booking['status'] == 'อนุมัติแล้ว'): ?>
                                    <a href="return-the-room.php?booking_id=<?php echo $booking['id']; ?>" 
                                       class="btn btn-secondary">
                                        <i class="fa-solid fa-undo"></i> คืนห้อง
                                    </a>
                                <?php endif; ?>
                                
                                <?php if ($booking['slip_image_url']): ?>
                                    <button type="button" class="btn btn-outline" onclick="showSlipModal('<?php echo htmlspecialchars($booking['slip_image_url']); ?>')">
                                        <i class="fa-solid fa-image"></i> ดูสลิป
                                    </button>
                                <?php endif; ?>
                                
                                <a href="booking-success.php?booking_id=<?php echo $booking['id']; ?>" 
                                   class="btn btn-outline">
                                    <i class="fa-solid fa-eye"></i> ดูรายละเอียด
                                </a>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="no-bookings">
                        <i class="fa-solid fa-clock-rotate-left"></i>
                        <h2>ไม่พบประวัติการจอง</h2>
                        <p>ไม่มีการจองที่ตรงกับเงื่อนไขการค้นหา</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

<!-- Slip Modal -->
<div id="slipModal" style="display:none; position:fixed; z-index:9999; left:0; top:0; width:100vw; height:100vh; background:rgba(0,0,0,0.6); align-items:center; justify-content:center;">
    <div style="background:#fff; border-radius:16px; padding:30px; max-width:90vw; max-height:90vh; box-shadow:0 8px 40px rgba(0,0,0,0.2); position:relative; display:flex; flex-direction:column; align-items:center;">
        <button onclick="closeSlipModal()" style="position:absolute; top:18px; right:18px; background:#dc3545; color:#fff; border:none; border-radius:50%; width:36px; height:36px; font-size:20px; cursor:pointer; display:flex; align-items:center; justify-content:center;">
            <i class="fa-solid fa-times"></i>
        </button>
        <img id="slipImage" src="" alt="Slip Image" style="max-width:80vw; max-height:70vh; border-radius:12px; box-shadow:0 2px 10px rgba(0,0,0,0.08); margin-bottom:10px;" />
        <a id="slipDownload" href="#" target="_blank" class="btn btn-primary" style="margin-top:10px;">
            <i class="fa-solid fa-download"></i> ดาวน์โหลดสลิป
        </a>
    </div>
</div>

<script>
function showSlipModal(url) {
    document.getElementById('slipImage').src = url;
    document.getElementById('slipDownload').href = url;
    document.getElementById('slipModal').style.display = 'flex';
}
function closeSlipModal() {
    document.getElementById('slipModal').style.display = 'none';
    document.getElementById('slipImage').src = '';
}
</script>
</body>
</html>
