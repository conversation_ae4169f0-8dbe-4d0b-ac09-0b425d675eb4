<!-- ... โค้ด HTML ทั้งหมดของคุณเหมือนเดิม ... -->

<!-- ✨ ส่วน Script ที่ต้องแก้ไข ✨ -->
<script>
    // --- ส่วนคำนวณราคาและแสดงชื่อไฟล์ (ใช้โค้ดเดิมของคุณได้เลย) ---
    // ... (Your existing price calculation and file display logic) ...

    // --- ส่วน Logic ใหม่สำหรับการส่งฟอร์มและแสดง Pop-up ---
    const bookingForm = document.getElementById('bookingForm'); // ตรวจสอบให้แน่ใจว่า <form> ของคุณมี id="bookingForm"
    const submitBtn = document.querySelector('.btn-confirm-booking'); // ปุ่มยืนยัน
    const successPopup = document.getElementById('successPopupOverlay'); // Pop-up
    const closePopupButton = document.getElementById('closePopupButton'); // ปุ่มปิด Pop-up

    bookingForm.addEventListener('submit', function(event) {
        // 1. หยุดการส่งฟอร์มแบบปกติ (ไม่ให้หน้าเว็บโหลดใหม่)
        event.preventDefault();

        // 2. ตรวจสอบว่ากรอกข้อมูลครบหรือไม่
        if (!bookingForm.checkValidity()) {
            bookingForm.reportValidity();
            return;
        }

        // ทำให้ปุ่มกดไม่ได้ชั่วคราวเพื่อป้องกันการกดซ้ำ
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fa-solid fa-spinner fa-spin"></i> กำลังส่งข้อมูล...';

        // 3. รวบรวมข้อมูลทั้งหมดจากฟอร์ม
        const formData = new FormData(bookingForm);

        // 4. ส่งข้อมูลไปที่ process-booking.php แบบเบื้องหลัง
        fetch('process-booking.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json()) // รอรับการตอบกลับแบบ JSON
        .then(data => {
            // 5. เมื่อได้รับการตอบกลับ
            if (data.status === 'success') {
                // ถ้าสำเร็จ: แสดง Pop-up
                successPopup.style.display = 'flex';
                setTimeout(() => successPopup.classList.add('show'), 10);
            } else {
                // ถ้าล้มเหลว: แสดงข้อความเตือน
                alert('เกิดข้อผิดพลาด: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('ไม่สามารถเชื่อมต่อกับเซิร์ฟเวอร์ได้');
        })
        .finally(() => {
            // คืนสภาพปุ่มให้กดได้เหมือนเดิม ไม่ว่าจะสำเร็จหรือล้มเหลว
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fa-solid fa-paper-plane"></i> ยืนยันการจอง';
        });
    });

    // ส่วนการปิด Pop-up และรีเซ็ตฟอร์ม (เหมือนเดิม)
    closePopupButton.addEventListener('click', function() {
        successPopup.classList.remove('show');
        setTimeout(() => {
            successPopup.style.display = 'none';
            bookingForm.reset();
            // เรียกใช้ฟังก์ชันคำนวณราคาอีกครั้งเพื่อให้ค่ากลับเป็น 0
            updatePriceSummary(); 
            // รีเซ็ตชื่อไฟล์
            document.getElementById('file-name-display').textContent = 'ยังไม่ได้เลือกไฟล์'; 
        }, 300);
    });
</script>