<?php
session_start();
require('connect.php');

// ตรวจสอบสิทธิ์การเข้าใช้งาน (employee เท่านั้น)
if (!isset($_SESSION['id_account']) || !isset($_SESSION['role_account']) || $_SESSION['role_account'] !== 'employee') {
    http_response_code(403);
    exit('Unauthorized');
}

if (isset($_GET['id'])) {
    $room_id = intval($_GET['id']);
    
    $query = "SELECT * FROM room WHERE id_room = $room_id";
    $result = mysqli_query($connect, $query);
    
    if ($row = mysqli_fetch_assoc($result)) {
        // ส่งข้อมูลกลับเป็น JSON
        header('Content-Type: application/json');
        echo json_encode($row);
    } else {
        http_response_code(404);
        echo json_encode(['error' => 'ไม่พบข้อมูลห้องพัก']);
    }
} else {
    http_response_code(400);
    echo json_encode(['error' => 'ไม่ระบุ ID ห้องพัก']);
}
?>
