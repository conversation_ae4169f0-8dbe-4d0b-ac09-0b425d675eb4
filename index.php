<?php
session_start();
require('connect.php');

if (!isset($_SESSION['id_account']) || !isset($_SESSION['role_account'])) {
    die(header('Location: form-login.php'));
} elseif (isset($_GET['logout'])) {
    session_destroy();
    die(header('Location: form-login.php'));
} else {
    $id_account = $_SESSION['id_account'];
    $query_show = "SELECT username_account, role_account, images_account FROM account WHERE id_account = '$id_account'";
    $call_back_show = mysqli_query($connect, $query_show);
    $result_show = mysqli_fetch_assoc($call_back_show);
}

// ดึงข้อมูลห้องพักจากฐานข้อมูล (แก้ไขให้ใช้ตาราง room)
$query = "SELECT * FROM room WHERE status = 'available' ORDER BY room_number ASC";
$result = mysqli_query($connect, $query);
$rooms = [];
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $rooms[] = $row;
    }
}

// --- ✨ โค้ดที่แก้ไข: ตรวจสอบไฟล์ปัจจุบันเพื่อกำหนดเมนูที่ active โดยอัตโนมัติ ---
$current_page_file = basename($_SERVER['SCRIPT_NAME']); // ดึงชื่อไฟล์ปัจจุบัน เช่น 'index.php'

switch ($current_page_file) {
    case 'index.php':
        $currentPage = 'booking';
        break;
    case 'history.php': // ต้องสร้างไฟล์ history.php
        $currentPage = 'history';
        break;
    case 'profile.php': // ต้องสร้างไฟล์ profile.php
        $currentPage = 'profile';
        break;
    case 'change-password.php': // ต้องสร้างไฟล์ change-password.php
        $currentPage = 'password';
        break;
    default:
        $currentPage = ''; // ถ้าไม่ตรงกับหน้าที่กำหนด ก็ไม่ต้อง active
}

// ดึงประเภทห้องพักทั้งหมด
$room_types = [];
if (!empty($rooms)) {
    $room_types = array_unique(array_column($rooms, 'room_type'));
}
$selected_type = isset($_GET['type']) ? $_GET['type'] : 'ทั้งหมด';

// ฟังก์ชันแปลงประเภทห้องเป็นภาษาไทย
function getRoomTypeThai($type) {
    $types = [
        'Standard' => 'ห้องมาตรฐาน',
        'Deluxe' => 'ห้องดีลักซ์',
        'Suite' => 'ห้องสวีท',
        'Family' => 'ห้องครอบครัว',
        'VIP' => 'ห้องวีไอพี'
    ];
    return $types[$type] ?? $type;
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - ระบบจัดการรีสอร์ท</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Sarabun:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: #7b43ff; /* สีม่วงหลัก */
            --light-primary-color: #f3f0ff; /* สีม่วงอ่อนสำหรับ hover */
            --text-color: #495057;
            --text-secondary-color: #6c757d;
            --sidebar-bg: #ffffff;
            --main-bg: #f9fafb;
            --border-color: #e7e7e7;
            --success-color: #28a745;
            --info-color: #17a2b8;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Sarabun', sans-serif;
            background: #f8fafc;
            color: var(--text-color);
            min-height: 100vh;
        }
        
        .main-content {
            margin-left: 280px; 
            padding: 0;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 280px;
            height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 20px;
            display: flex;
            flex-direction: column;
            box-sizing: border-box;
            border-right: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .sidebar-header {
            padding: 0 10px;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
        }
        
        .logo {
            display: flex;
            align-items: center;
            font-size: 22px;
            font-weight: 700;
            color: #343a40;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .logo:hover {
            color: var(--primary-color);
            transform: translateY(-2px);
        }
        
        .logo i {
            color: var(--primary-color);
            font-size: 30px;
            margin-right: 12px;
            transition: all 0.3s ease;
        }
        
        .user-profile {
            display: flex;
            align-items: center;
            padding: 15px 10px;
            margin-bottom: 15px;
            border-top: 1px solid var(--border-color);
            border-bottom: 1px solid var(--border-color);
            background: rgba(255, 255, 255, 0.5);
            border-radius: 12px;
        }
        
        .user-avatar {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), #9c6fff);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 22px;
            margin-right: 15px;
            box-shadow: 0 4px 15px rgba(123, 67, 255, 0.3);
        }
        
        .user-info h5 {
            margin: 0;
            font-weight: 700;
            font-size: 16px;
        }
        
        .user-info p {
            margin: 0;
            font-size: 14px;
            color: var(--text-secondary-color);
        }
        
        .sidebar-nav {
            list-style: none;
            padding: 0;
            margin: 0;
            flex-grow: 1; 
        }
        
        .nav-header {
            padding: 15px 15px 8px;
            font-size: 12px;
            font-weight: 700;
            color: #adb5bd;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .sidebar-nav li a {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            margin: 4px 0;
            color: var(--text-color);
            text-decoration: none;
            font-size: 16px;
            font-weight: 500;
            border-radius: 12px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .sidebar-nav li a::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 0;
            background: linear-gradient(90deg, var(--primary-color), #9c6fff);
            transition: width 0.3s ease;
            z-index: -1;
        }
        
        .sidebar-nav li a:hover::before {
            width: 100%;
        }
        
        .sidebar-nav li a i {
            width: 20px;
            margin-right: 15px;
            text-align: center;
            font-size: 18px;
            color: var(--text-secondary-color);
            transition: all 0.3s ease;
        }
        
        .sidebar-nav li.active a {
            background: linear-gradient(135deg, var(--primary-color), #9c6fff);
            color: #ffffff;
            font-weight: 700;
            box-shadow: 0 4px 15px rgba(123, 67, 255, 0.3);
            transform: translateX(5px);
        }
        
        .sidebar-nav li.active a i {
            color: #ffffff;
            transform: scale(1.1);
        }
        
        .sidebar-nav li a:hover {
            color: #fff;
            transform: translateX(5px);
        }
        
        .sidebar-nav li a:hover i {
            color: #fff;
            transform: scale(1.1);
        }
        
        .sidebar-nav li.active a:hover {    
            background: linear-gradient(135deg, var(--primary-color), #9c6fff);
            color: #ffffff;
            cursor: default;
            transform: translateX(5px);
        }
        
        .sidebar-nav .logout-item {
             margin-top: auto; 
        }
        
        .sidebar-nav .logout-item a {
            background: linear-gradient(135deg, #dc3545, #e74c3c);
            color: white;
            border-radius: 12px;
            margin: 0;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
        }
        
        .sidebar-nav .logout-item a:hover {
            background: linear-gradient(135deg, #c82333, #d63384);
            transform: translateX(5px);
        }
        
        /* ===== MINIMALIST DESIGN ===== */
        
        /* Header */
        .page-header {
            background: white;
            padding: 40px 60px;
            border-bottom: 1px solid #e9ecef;
            position: relative;
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
        }

        .page-title {
            font-size: 2.5rem;
            font-weight: 300;
            color: #2c3e50;
            margin-bottom: 10px;
            letter-spacing: -0.5px;
        }

        .page-subtitle {
            font-size: 1.1rem;
            color: #6c757d;
            font-weight: 400;
        }

        /* Filter Bar */
        .filter-container {
            background: white;
            padding: 30px 60px;
            border-bottom: 1px solid #e9ecef;
        }

        .filter-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            gap: 30px;
        }

        .filter-label {
            font-size: 1rem;
            font-weight: 500;
            color: #495057;
            white-space: nowrap;
        }

        .filter-tabs {
            display: flex;
            gap: 5px;
            background: #f8f9fa;
            padding: 5px;
            border-radius: 12px;
        }

        .filter-tab {
            padding: 10px 20px;
            border: none;
            background: transparent;
            color: #6c757d;
            font-size: 0.9rem;
            font-weight: 500;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .filter-tab.active,
        .filter-tab:hover {
            background: white;
            color: var(--primary-color);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        /* Room Grid */
        .room-container {
            padding: 60px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .room-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 40px;
        }

        .room-card {
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            border: 1px solid #f1f3f4;
        }

        .room-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 8px 40px rgba(0, 0, 0, 0.12);
        }

        .room-image {
            height: 220px;
            background: #f8f9fa;
            position: relative;
            overflow: hidden;
        }

        .room-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .room-card:hover .room-image img {
            transform: scale(1.05);
        }

        .room-image .placeholder {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #adb5bd;
            font-size: 3rem;
        }

        .room-status {
            position: absolute;
            top: 15px;
            right: 15px;
            background: var(--success-color);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            letter-spacing: 0.5px;
        }

        .room-content {
            padding: 30px;
        }

        .room-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 20px;
        }

        .room-number {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2c3e50;
            letter-spacing: -0.5px;
        }

        .room-type {
            background: #f8f9fa;
            color: #6c757d;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            letter-spacing: 0.5px;
        }

        .room-price {
            font-size: 2rem;
            font-weight: 700;
            color: var(--success-color);
            margin-bottom: 15px;
            letter-spacing: -1px;
        }

        .room-description {
            color: #6c757d;
            line-height: 1.6;
            margin-bottom: 25px;
            font-size: 0.95rem;
        }

        .room-features {
            display: flex;
            gap: 15px;
            margin-bottom: 25px;
            flex-wrap: wrap;
        }

        .feature {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #6c757d;
            font-size: 0.85rem;
        }

        .feature i {
            color: var(--primary-color);
            font-size: 0.9rem;
        }

        .room-actions {
            display: flex;
            gap: 12px;
        }

        .btn {
            flex: 1;
            padding: 12px 20px;
            border: none;
            border-radius: 10px;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            text-align: center;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-outline {
            background: transparent;
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
        }

        .btn-outline:hover {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: #6a3ce7;
            transform: translateY(-2px);
        }

        /* No Rooms */
        .no-rooms {
            text-align: center;
            padding: 80px 40px;
            background: white;
            border-radius: 16px;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
        }

        .no-rooms i {
            font-size: 4rem;
            color: #adb5bd;
            margin-bottom: 20px;
        }

        .no-rooms h2 {
            font-size: 1.8rem;
            color: #2c3e50;
            margin-bottom: 10px;
            font-weight: 400;
        }

        .no-rooms p {
            color: #6c757d;
            font-size: 1rem;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .page-header,
            .filter-container,
            .room-container {
                padding: 30px 20px;
            }
            
            .filter-content {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }
            
            .room-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }
            
            .room-actions {
                flex-direction: column;
            }
        }

        /* Animations */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .room-card {
            animation: fadeIn 0.6s ease forwards;
        }

        .room-card:nth-child(1) { animation-delay: 0.1s; }
        .room-card:nth-child(2) { animation-delay: 0.2s; }
        .room-card:nth-child(3) { animation-delay: 0.3s; }
        .room-card:nth-child(4) { animation-delay: 0.4s; }
        .room-card:nth-child(5) { animation-delay: 0.5s; }
        .room-card:nth-child(6) { animation-delay: 0.6s; }
    </style>
</head>
<body>
    
    <aside class="sidebar">
        <div class="sidebar-header">
            <a href="index.php" class="logo">
                <i class="fa-solid fa-hotel"></i>
                <span>ระบบจัดการรีสอร์ท</span>
            </a>
        </div>
        <div class="user-profile">
            <div class="user-avatar">
                <i class="fa-solid fa-user"></i>
            </div>
            <div class="user-info">
                <h5><?php echo htmlspecialchars($result_show['username_account']); ?></h5>
                <p><?php echo htmlspecialchars($result_show['role_account']); ?></p>
            </div>
        </div>
        
        <ul class="sidebar-nav">
            <li class="nav-header">ระบบการจอง</li>
            <li class="<?php if ($currentPage == 'booking') echo 'active'; ?>">
                <a href="index.php"><i class="fa-solid fa-calendar-days"></i><span>จองห้องพัก</span></a>
            </li>
            <li class="<?php if ($currentPage == 'history') echo 'active'; ?>">
                <a href="history.php"><i class="fa-solid fa-clock-rotate-left"></i><span>ประวัติการจอง</span></a>
            </li>

            <li class="nav-header">บริการเสริม</li>
            <li class="<?php if ($currentPage == 'beverage') echo 'active'; ?>">
                <a href=""><i class="fa-solid fa-mug-hot"></i><span>เครื่องดื่ม</span></a>
            </li>

            <li class="<?php if ($currentPage == 'beverage') echo 'active'; ?>">
                <a href=""><i class="fa-solid fa-cookie-bite"></i><span>เบเกอรี่</span></a>
            </li>

            <li class="nav-header">เกี่ยวกับฉัน</li>
            <li class="<?php if ($currentPage == 'profile') echo 'active'; ?>">
                <a href="profile.php"><i class="fa-solid fa-user-gear"></i><span>โปรไฟล์</span></a>
            </li>
            <li class="<?php if ($currentPage == 'password') echo 'active'; ?>">
                <a href="change-password.php"><i class="fa-solid fa-lock"></i><span>เปลี่ยนรหัสผ่าน</span></a>
            </li>
            
            <li class="logout-item"> 
                <a href="?logout=1"><i class="fa-solid fa-right-from-bracket"></i><span>ออกจากระบบ</span></a>
            </li>
        </ul>
    </aside>

    <div class="main-content">
        <!-- Page Header -->
        <header class="page-header">
            <div class="header-content">
                <h1 class="page-title">ห้องพักของเรา</h1>
                <p class="page-subtitle">เลือกห้องพักที่เหมาะกับคุณ</p>
            </div>
        </header>

        <!-- Filter Bar -->
        <div class="filter-container">
            <div class="filter-content">
                <span class="filter-label">ประเภทห้อง:</span>
                <div class="filter-tabs">
                    <button class="filter-tab active" onclick="filterRooms('ทั้งหมด')">ทั้งหมด</button>
                    <?php foreach ($room_types as $type): ?>
                        <button class="filter-tab" onclick="filterRooms('<?php echo htmlspecialchars($type); ?>')">
                            <?php echo getRoomTypeThai($type); ?>
                        </button>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <!-- Room Grid -->
        <div class="room-container">
            <div class="room-grid">
                <?php if (!empty($rooms)): ?>
                    <?php foreach ($rooms as $room): ?>
                        <div class="room-card" data-type="<?php echo htmlspecialchars($room['room_type']); ?>">
                            <div class="room-image">
                                <?php if (!empty($room['image'])): ?>
                                    <img src="<?php echo htmlspecialchars($room['image']); ?>" alt="ห้อง <?php echo htmlspecialchars($room['room_number']); ?>">
                                <?php else: ?>
                                    <div class="placeholder">
                                        <i class="fa-solid fa-bed"></i>
                                    </div>
                                <?php endif; ?>
                                <div class="room-status">ว่าง</div>
                            </div>
                            
                            <div class="room-content">
                                <div class="room-header">
                                    <div class="room-number">ห้อง <?php echo htmlspecialchars($room['room_number']); ?></div>
                                    <div class="room-type"><?php echo getRoomTypeThai($room['room_type']); ?></div>
                                </div>
                                
                                <div class="room-price">฿<?php echo number_format($room['price']); ?></div>
                                
                                <div class="room-description">
                                    <?php echo htmlspecialchars($room['description'] ?: 'ห้องพักที่สะดวกสบาย พร้อมสิ่งอำนวยความสะดวกครบครัน'); ?>
                                </div>
                                
                                <div class="room-features">
                                    <span class="feature"><i class="fa-solid fa-wifi"></i> WiFi</span>
                                    <span class="feature"><i class="fa-solid fa-snowflake"></i> แอร์</span>
                                    <span class="feature"><i class="fa-solid fa-tv"></i> ทีวี</span>
                                    <span class="feature"><i class="fa-solid fa-shower"></i> ห้องน้ำ</span>
                                </div>
                                
                                <div class="room-actions">
                                    <a href="room-details.php?room_id=<?php echo $room['id_room']; ?>" class="btn btn-outline">
                                        <i class="fa-solid fa-eye"></i> ดูรายละเอียด
                                    </a>
                                    <a href="booking-form.php?room_id=<?php echo $room['id_room']; ?>" class="btn btn-primary">
                                        <i class="fa-solid fa-calendar-check"></i> จองเลย
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="no-rooms">
                        <i class="fa-solid fa-bed"></i>
                        <h2>ไม่มีห้องพักว่างในขณะนี้</h2>
                        <p>กรุณาลองใหม่อีกครั้งในภายหลัง</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script>
        function filterRooms(type) {
            const rooms = document.querySelectorAll('.room-card');
            const filterTabs = document.querySelectorAll('.filter-tab');
            
            // Update active tab
            filterTabs.forEach(tab => {
                tab.classList.remove('active');
                if (tab.textContent.trim() === getRoomTypeThai(type) || (type === 'ทั้งหมด' && tab.textContent.trim() === 'ทั้งหมด')) {
                    tab.classList.add('active');
                }
            });
            
            // Filter rooms
            rooms.forEach(room => {
                const roomType = room.getAttribute('data-type');
                if (type === 'ทั้งหมด' || roomType === type) {
                    room.style.display = 'block';
                    room.style.animation = 'fadeIn 0.6s ease forwards';
                } else {
                    room.style.display = 'none';
                }
            });
        }

        function getRoomTypeThai(type) {
            const types = {
                'Standard': 'ห้องมาตรฐาน',
                'Deluxe': 'ห้องดีลักซ์',
                'Suite': 'ห้องสวีท',
                'Family': 'ห้องครอบครัว',
                'VIP': 'ห้องวีไอพี'
            };
            return types[type] || type;
        }
    </script>
</body>
</html>
    