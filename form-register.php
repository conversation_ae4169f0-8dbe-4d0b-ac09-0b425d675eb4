<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>สร้างบัญชีใหม่</title>

    <!-- นำเข้าฟอนต์ภาษาไทย -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Sarabun:wght@400;700&display=swap" rel="stylesheet">

    <style>
        /* CSS สำหรับตกแต่งหน้าเว็บ */
        body {
            margin: 0;
            font-family: 'Sarabun', sans-serif;
            background-color: #e9ebee; /* สีพื้นหลังเทาอ่อน */
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
            box-sizing: border-box;
        }

        .register-container {
            background-color: #ffffff;
            padding: 40px;
            border-radius: 25px; /* ขอบมน */
            width: 100%;
            max-width: 420px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .title {
            font-size: 2rem;
            font-weight: 700;
            color: #333;
            margin-top: 0;
            margin-bottom: 25px;
        }

        .title-blue {
            color: #38b6ff; /* สีฟ้า */
        }

        .profile-avatar {
            width: 160px;
            height: 160px;
            border-radius: 50%; /* ทำให้เป็นวงกลม */
            object-fit: cover;
            margin-bottom: 35px;
        }
        
        .input-group {
            margin-bottom: 20px;
            text-align: left; /* จัดหัวข้อชิดซ้าย */
        }

        .input-group label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-size: 0.9rem;
            font-weight: 600;
            margin-left: 20px;
        }
        
        .input-group input {
            width: 100%;
            padding: 16px 25px;
            border: none;
            background-color: #f0f2f5; /* สีเทาอ่อน */
            border-radius: 30px; /* ทรงแคปซูล */
            font-size: 1rem;
            font-family: 'Sarabun', sans-serif;
            box-sizing: border-box;
        }

        .input-group input:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(56, 182, 255, 0.5);
        }
        
        .btn-register {
            width: 100%;
            padding: 16px;
            border: none;
            border-radius: 30px; /* ทรงแคปซูล */
            font-size: 1.1rem;
            font-weight: 700;
            cursor: pointer;
            margin-top: 15px;
            background-color: #38b6ff; /* สีฟ้า */
            color: #ffffff;
            transition: background-color 0.3s ease;
        }
        
        .btn-register:hover {
            background-color: #1f9ce0;
        }
        
        .login-link {
            display: block; /* ทำให้ขึ้นบรรทัดใหม่ */
            margin-top: 25px;
            font-size: 0.9rem;
            color: #555;
            text-decoration: none;
        }

        .login-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    
    <div class="register-container">
        <!-- เปลี่ยน h1 ให้เหมือนในรูป -->
        <h1 class="title">สมัครส<span class="title-blue">มาชิก</span></h1>
        
        <!-- !! อย่าลืม !! เพิ่มรูปภาพที่นี่ -->
        <img src="resort-image.jpg" alt="Resort" class="profile-avatar">

        <!-- ผมปรับปรุง form จากโค้ดที่คุณให้มา -->
        <form action="process-register.php" method="POST">
            
            <div class="input-group">
                <label for="username">ชื่อผู้ใช้</label>
                <!-- ผมเอา placeholder ออก เพราะเรามี label แล้ว -->
                <input id="username" name="username_account" type="text" required>
            </div>

            <div class="input-group">
                <label for="email">อีเมล</label>
                <input id="email" name="email_account" type="email" required>
            </div>

            <div class="input-group">
                <label for="password">รหัสผ่าน</label>
                <input id="password" name="password_account1" type="password" required>
            </div>

            <div class="input-group">
                <label for="confirm-password">ยืนยันรหัสผ่าน</label>
                <input id="confirm-password" name="password_account2" type="password" required>
            </div>
            
            <!-- เปลี่ยน <button> ให้มี class สำหรับตกแต่ง -->
            <button type="submit" class="btn-register">สร้างบัญชี</button>
        </form>
        
        <!-- เปลี่ยน <a> ให้มี class สำหรับตกแต่ง -->
        <a href="form-login.php" class="login-link">มีบัญชีแล้วใช่ไหม</a>
    </div>
    
</body>
</html>