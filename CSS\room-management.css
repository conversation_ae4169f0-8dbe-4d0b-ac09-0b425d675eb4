/* ===== ROOM MANAGEMENT STYLES ===== */

/* Filter Bar */
.filter-bar { 
    display: flex; 
    gap: 15px; 
    margin-bottom: 25px; 
    padding: 20px; 
    background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%); 
    border-radius: 15px; 
    box-shadow: 0 8px 25px rgba(123,97,255,0.08); 
    align-items: center; 
    border: 1px solid rgba(123,97,255,0.1);
}

.filter-bar form { 
    display: flex; 
    gap: 15px; 
    align-items: center; 
    flex-wrap: wrap; 
}

.filter-bar label { 
    font-weight: 600; 
    color: var(--text-secondary); 
    font-size: 14px; 
}

.filter-bar input[type="text"], 
.filter-bar select { 
    padding: 10px 15px; 
    border-radius: 8px; 
    border: 1px solid var(--border-color-light); 
    font-family: 'Sarabun', sans-serif; 
    font-size: 14px; 
    background-color: #fff;
    transition: all 0.3s ease;
}

.filter-bar input[type="text"]:focus, 
.filter-bar select:focus { 
    outline: none; 
    border-color: var(--primary-color); 
    box-shadow: 0 0 0 3px rgba(123,97,255,0.1);
}

.filter-bar .btn { 
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%); 
    color: white; 
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.filter-bar .btn:hover { 
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(123,97,255,0.3);
}

/* Statistics Cards */
.stats-container { 
    display: grid; 
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); 
    gap: 20px; 
    margin-bottom: 30px; 
}

.stat-card { 
    display: flex; 
    align-items: center; 
    padding: 25px; 
    border-radius: 15px; 
    background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
    box-shadow: 0 8px 25px rgba(0,0,0,0.08); 
    border: 1px solid rgba(123,97,255,0.1);
    transition: all 0.3s ease;
}

.stat-card:hover { 
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(123,97,255,0.15);
}

.stat-icon { 
    font-size: 40px; 
    color: var(--primary-color); 
    margin-right: 20px; 
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    background: linear-gradient(135deg, rgba(123,97,255,0.1) 0%, rgba(123,97,255,0.05) 100%);
}

.stat-icon.available { 
    color: var(--green-color); 
    background: linear-gradient(135deg, rgba(40,167,69,0.1) 0%, rgba(40,167,69,0.05) 100%);
}

.stat-icon.occupied { 
    color: var(--red-color); 
    background: linear-gradient(135deg, rgba(220,53,69,0.1) 0%, rgba(220,53,69,0.05) 100%);
}

.stat-icon.revenue { 
    color: var(--orange-color); 
    background: linear-gradient(135deg, rgba(253,126,20,0.1) 0%, rgba(253,126,20,0.05) 100%);
}

.stat-content h3 { 
    margin: 0 0 8px 0; 
    font-size: 28px; 
    font-weight: 700; 
    color: var(--text-color); 
}

.stat-content p { 
    margin: 0; 
    font-size: 14px; 
    color: var(--text-secondary); 
    font-weight: 500;
}

/* Room Grid */
.room-grid { 
    display: grid; 
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr)); 
    gap: 25px; 
}

.room-card { 
    background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%); 
    border-radius: 20px; 
    box-shadow: 0 10px 30px rgba(0,0,0,0.08); 
    overflow: hidden; 
    display: flex; 
    flex-direction: column; 
    border: 1px solid rgba(123,97,255,0.1);
    transition: all 0.3s ease;
}

.room-card:hover { 
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(123,97,255,0.15);
}

.room-card-header { 
    position: relative;
    padding: 20px 20px 0 20px;
}

.room-image-container { 
    position: relative; 
    width: 100%; 
    height: 220px; 
    background: linear-gradient(135deg, #f0f0f0 0%, #e0e0e0 100%); 
    display: flex; 
    align-items: center; 
    justify-content: center; 
    overflow: hidden; 
    border-radius: 15px; 
    margin-bottom: 15px; 
}

.room-card-img { 
    width: 100%; 
    height: 100%; 
    object-fit: cover; 
    transition: transform 0.3s ease;
}

.room-card:hover .room-card-img { 
    transform: scale(1.05);
}

.room-card-img-placeholder { 
    display: flex; 
    flex-direction: column; 
    align-items: center; 
    justify-content: center; 
    color: #999; 
    font-size: 24px; 
}

.room-card-img-placeholder p { 
    margin: 10px 0 0 0; 
    font-size: 14px; 
}

.room-overlay { 
    position: absolute; 
    top: 0; 
    left: 0; 
    width: 100%; 
    height: 100%; 
    background: linear-gradient(135deg, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.5) 100%); 
    border-radius: 15px; 
    display: flex; 
    align-items: center; 
    justify-content: center; 
    opacity: 0; 
    transition: opacity 0.3s ease; 
}

.room-card:hover .room-overlay { 
    opacity: 1; 
}

.room-actions { 
    display: flex; 
    gap: 15px; 
}

.btn-overlay { 
    padding: 12px 16px; 
    border-radius: 10px; 
    font-size: 16px; 
    font-weight: 600; 
    cursor: pointer; 
    display: inline-flex; 
    align-items: center; 
    justify-content: center; 
    gap: 8px; 
    transition: all 0.3s ease; 
    border: none;
    color: white;
    text-decoration: none;
}

.btn-overlay.btn-edit { 
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
}

.btn-overlay.btn-delete { 
    background: linear-gradient(135deg, var(--red-color) 0%, #c82333 100%);
}

.btn-overlay:hover { 
    transform: scale(1.1);
    box-shadow: 0 8px 20px rgba(0,0,0,0.3);
}

.room-status { 
    position: absolute;
    top: 20px;
    right: 20px;
    padding: 8px 16px; 
    border-radius: 20px; 
    font-size: 12px; 
    font-weight: 700; 
    color: #fff; 
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.status-ว่าง { 
    background: linear-gradient(135deg, var(--green-color) 0%, #20893a 100%); 
}

.status-ไม่ว่าง { 
    background: linear-gradient(135deg, var(--orange-color) 0%, #e65a00 100%); 
}

.room-card-body { 
    padding: 20px; 
    flex-grow: 1; 
}

.room-card h3 { 
    margin: 0 0 15px 0; 
    font-size: 20px; 
    font-weight: 700; 
    color: var(--text-color);
    line-height: 1.3;
}

.room-card p { 
    margin: 8px 0; 
    color: var(--text-secondary); 
    font-size: 14px; 
    display: flex;
    align-items: center;
    gap: 8px;
}

.room-type { 
    font-weight: 500;
    color: var(--primary-color) !important;
}

.room-price { 
    font-size: 18px !important; 
    font-weight: 700; 
    color: var(--primary-dark) !important; 
}

.room-details { 
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid rgba(123,97,255,0.1);
}

.room-id { 
    font-size: 12px; 
    color: var(--text-secondary); 
    font-weight: 500;
}

.room-card-footer { 
    padding: 20px; 
    border-top: 1px solid rgba(123,97,255,0.1); 
    display: flex; 
    gap: 10px; 
    background: linear-gradient(135deg, #fcfcff 0%, #f8f9ff 100%);
    flex-wrap: wrap;
}

.btn-edit { 
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%); 
    color: #333; 
    border: none;
    font-weight: 600;
} 

.btn-edit:hover { 
    background: linear-gradient(135deg, #e0a800 0%, #d39e00 100%);
    transform: translateY(-2px);
}

.btn-delete { 
    background: linear-gradient(135deg, var(--red-color) 0%, #c82333 100%); 
    color: white; 
    border: none;
    font-weight: 600;
} 

.btn-delete:hover { 
    background: linear-gradient(135deg, #c82333 0%, #b82c3a 100%);
    transform: translateY(-2px);
}

.btn-view { 
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%); 
    color: white; 
    border: none;
    font-weight: 600;
} 

.btn-view:hover { 
    background: linear-gradient(135deg, var(--primary-dark) 0%, #4a3bb3 100%);
    transform: translateY(-2px);
}

/* Form Styles */
.form-container { 
    background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%); 
    padding: 40px; 
    border-radius: 20px; 
    box-shadow: 0 15px 40px rgba(123,97,255,0.1); 
    max-width: 800px; 
    margin: 0 auto; 
    border: 1px solid rgba(123,97,255,0.1);
}

.form-group { 
    margin-bottom: 25px; 
}

.form-group label { 
    display: block; 
    font-weight: 600; 
    margin-bottom: 10px; 
    color: var(--text-color);
    font-size: 15px;
}

.form-group input[type="text"], 
.form-group input[type="number"], 
.form-group select, 
.form-group input[type="file"] { 
    width: 100%; 
    padding: 15px; 
    border: 2px solid var(--border-color-light); 
    border-radius: 10px; 
    font-size: 15px; 
    box-sizing: border-box; 
    font-family: 'Sarabun', sans-serif;
    transition: all 0.3s ease;
    background-color: #fff;
}

.form-group input[type="text"]:focus, 
.form-group input[type="number"]:focus, 
.form-group select:focus { 
    outline: none; 
    border-color: var(--primary-color); 
    box-shadow: 0 0 0 4px rgba(123,97,255,0.1);
}

.form-group input[type="file"] { 
    padding: 12px; 
    background: linear-gradient(135deg, #f8f9ff 0%, #f0edff 100%);
    border: 2px dashed var(--primary-color);
}

.form-actions { 
    margin-top: 40px; 
    display: flex; 
    justify-content: flex-end; 
    gap: 15px; 
}

.btn-secondary { 
    background: linear-gradient(135deg, var(--secondary-color) 0%, #e2e6ea 100%); 
    color: var(--text-secondary); 
    border: 1px solid var(--border-color-light); 
    font-weight: 600;
} 

.btn-secondary:hover { 
    background: linear-gradient(135deg, #e2e6ea 0%, #d1d5d8 100%);
    transform: translateY(-2px);
}

.current-image { 
    max-width: 200px; 
    height: auto; 
    margin-top: 15px; 
    border-radius: 10px; 
    border: 2px solid var(--border-color-light); 
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
    margin: 5% auto;
    padding: 0;
    border-radius: 20px;
    width: 90%;
    max-width: 600px;
    box-shadow: 0 25px 50px rgba(0,0,0,0.25);
    border: 1px solid rgba(123,97,255,0.1);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    padding: 15px 20px;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close:hover,
.close:focus {
    color: var(--primary-color);
}

.modal-header {
    padding: 20px 30px 0 30px;
    border-bottom: 1px solid rgba(123,97,255,0.1);
}

.modal-header h2 {
    margin: 0;
    color: var(--text-color);
    font-size: 24px;
    font-weight: 700;
}

.modal-body {
    padding: 30px;
}

.room-detail-image {
    margin-bottom: 20px;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.room-detail-image img {
    width: 100%;
    height: 250px;
    object-fit: cover;
}

.room-detail-info h3 {
    margin: 0 0 20px 0;
    font-size: 22px;
    font-weight: 700;
    color: var(--text-color);
}

.room-detail-info p {
    margin: 10px 0;
    font-size: 16px;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    gap: 10px;
}

.room-detail-info strong {
    color: var(--text-color);
    font-weight: 600;
    min-width: 80px;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
    color: white;
    text-transform: uppercase;
}

.modal-footer {
    padding: 20px 30px 30px 30px;
    border-top: 1px solid rgba(123,97,255,0.1);
    display: flex;
    gap: 15px;
    justify-content: flex-end;
}

/* Responsive Design */
@media (max-width: 768px) {
    .room-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-container {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .filter-bar form {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-bar input[type="text"],
    .filter-bar select {
        width: 100%;
    }
    
    .modal-content {
        width: 95%;
        margin: 10% auto;
    }
    
    .modal-body {
        padding: 20px;
    }
    
    .modal-footer {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .stats-container {
        grid-template-columns: 1fr;
    }
    
    .room-card-footer {
        flex-direction: column;
    }
    
    .room-card-footer .btn {
        width: 100%;
        justify-content: center;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Success/Error Messages */
.message {
    padding: 15px 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 10px;
}

.message.success {
    background: linear-gradient(135deg, rgba(40,167,69,0.1) 0%, rgba(40,167,69,0.05) 100%);
    color: var(--green-color);
    border: 1px solid rgba(40,167,69,0.2);
}

.message.error {
    background: linear-gradient(135deg, rgba(220,53,69,0.1) 0%, rgba(220,53,69,0.05) 100%);
    color: var(--red-color);
    border: 1px solid rgba(220,53,69,0.2);
}

.message.info {
    background: linear-gradient(135deg, rgba(123,97,255,0.1) 0%, rgba(123,97,255,0.05) 100%);
    color: var(--primary-color);
    border: 1px solid rgba(123,97,255,0.2);
} 