<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>รีสอร์ทสายบุรี - ล็อคอิน</title>
    
    <!-- Google Fonts for Thai Language -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Sarabun:wght@400;700&display=swap" rel="stylesheet">

    <style>
        /* CSS for the login page design */
        body {
            margin: 0;
            font-family: 'Sarabun', sans-serif; /* ใช้ฟอนต์ที่รองรับภาษาไทย */
            background-color: #e9ebee; /* สีพื้นหลังเทาอ่อนเหมือนในรูป */
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
            box-sizing: border-box;
        }

        .login-container {
            background-color: #ffffff;
            padding: 40px;
            border-radius: 25px; /* ทำให้ขอบมน */
            width: 100%;
            max-width: 400px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1); /* เพิ่มเงาเล็กน้อย */
        }

        .title {
            font-size: 2rem; /* ขนาดตัวอักษรหัวข้อ */
            font-weight: 700;
            color: #333;
            margin-top: 0;
            margin-bottom: 25px;
        }

        .title-blue {
            color: #38b6ff; /* สีฟ้าสำหรับคำว่า 'สายบุรี' */
        }

        .profile-avatar {
            width: 160px;
            height: 160px;
            border-radius: 50%; /* ทำให้รูปเป็นวงกลม */
            object-fit: cover; /* ป้องกันไม่ให้รูปบิดเบี้ยว */
            margin-bottom: 35px;
        }
        
        /* ตั้งค่าสำหรับกลุ่มของ Label และ Input */
        .input-group {
            margin-bottom: 20px;
            text-align: left; /* จัดให้ Label ชิดซ้าย */
        }

        .input-group label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-size: 0.9rem;
            font-weight: 600;
            margin-left: 20px; /* ขยับเข้ามาเล็กน้อย */
        }
        
        /* สไตล์ของช่องกรอกข้อมูล */
        .input-group input {
            width: 100%;
            padding: 16px 25px;
            border: none;
            background-color: #f0f2f5; /* สีเทาอ่อนสำหรับช่องกรอก */
            border-radius: 30px; /* ทำให้เป็นทรงแคปซูล */
            font-size: 1rem;
            box-sizing: border-box; /* สำคัญมากเพื่อให้ padding ไม่ทำให้กล่องขยายเกิน */
        }

        .input-group input:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(56, 182, 255, 0.5); /* เพิ่มเอฟเฟกต์เมื่อเลือก */
        }
        
        /* สไตล์ของปุ่มทั้งหมด */
        .btn {
            width: 100%;
            padding: 16px;
            border: none;
            border-radius: 30px; /* ทรงแคปซูล */
            font-size: 1.1rem;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            box-sizing: border-box;
            margin-bottom: 15px; /* ระยะห่างระหว่างปุ่ม */
        }

        .btn:active {
            transform: scale(0.98); /* เอฟเฟกต์เมื่อกด */
        }

        /* สไตล์ปุ่ม Login */
        .btn-login {
            background-color: #e4e6eb; /* สีเทาอ่อน */
            color: #000000;
        }

        .btn-login:hover {
            background-color: #d8dbe1;
        }

        /* สไตล์ปุ่ม Sign Up */
        .btn-signup {
            background-color: #38b6ff; /* สีฟ้าสดใส */
            color: #ffffff;
        }
        
        .btn-signup:hover {
            background-color: #1f9ce0;
        }
        
        /* ลิงก์ลืมรหัสผ่าน */
        .forgot-password {
            display: inline-block;
            margin-top: 20px;
            font-size: 0.9rem;
            color: #555;
            text-decoration: none;
        }

        .forgot-password:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    
    <div class="login-container">
        <img src="uploads/resort-image.png" alt="resort-image.png" class="profile-avatar">
        <h1 class="title">ระบบรีสอร์ท<span class="title-blue">สายบุรี</span></h1>
        

        <form action="process-login.php" method="POST">
            <div class="input-group">
                <label for="email">อีเมล</label>
                <input type="text" id="email" name="email_account" required>
            </div>
            <div class="input-group">
                <label for="password">รหัสผ่าน</label>
                <input type="password" id="password" name="password_account" required>
            </div>
            <button type="submit" class="btn btn-login">เข้าสู่ระบบ</button>
            <button type="button" class="btn btn-signup" onclick="window.location.href='form-register.php';">สมัครสมาชิก</button>
        </form>

        <a href="#" class="forgot-password">ลืมรหัสผ่าน</a>
    </div>

</body>
</html>