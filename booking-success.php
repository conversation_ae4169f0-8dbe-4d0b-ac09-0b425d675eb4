<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8"><title>การจองสำเร็จ</title>
    <link href="https://fonts.googleapis.com/css2?family=Sarabun:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        body {
            font-family: 'Sarabun', sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background-color: #f0f9f5;
            margin: 0;
        }
        .success-box {
            text-align: center;
            background: white;
            padding: 40px 50px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .success-box i {
            font-size: 50px;
            color: #198754;
            margin-bottom: 20px;
        }
        h1 { margin-bottom: 10px; }
        p { color: #6c757d; line-height: 1.6; }
        
        /* --- ✨ CSS ที่ปรับปรุงสำหรับปุ่ม --- */
        .button-container {
            margin-top: 25px;
            display: flex;
            justify-content: center;
            gap: 15px; /* เพิ่มระยะห่างระหว่างปุ่ม */
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 500;
            border: 2px solid transparent; /* เตรียมพื้นที่สำหรับ border */
            transition: all 0.2s ease-in-out;
        }
        /* ปุ่มหลัก (Primary) */
        .btn-primary {
            background-color: #0d6efd;
            color: white;
        }
        .btn-primary:hover {
            background-color: #0b5ed7;
        }
        /* ปุ่มรอง (Secondary) */
        .btn-secondary {
            background-color: #f8f9fa;
            color: #495057;
            border: 2px solid #dee2e6;
        }
        .btn-secondary:hover {
            background-color: #e2e6ea;
        }
    </style>
</head>
<body>
    <div class="success-box">
        <i class="fa-solid fa-circle-check"></i>
        <h1>ทำการจองสำเร็จ!</h1>
        <p>การจองของท่านได้ถูกส่งเข้าระบบเรียบร้อยแล้ว<br>และกำลังรอการอนุมัติจากเจ้าหน้าที่</p>
        
        <!-- ✨✨✨ จุดที่แก้ไขและเพิ่มปุ่ม ✨✨✨ -->
        <div class="button-container">
            <a href="index.php" class="btn btn-secondary">กลับสู่หน้าหลัก</a>
            <a href="history.php" class="btn btn-primary">ดูประวัติการจอง</a>
        </div>

    </div>
</body>
</html>