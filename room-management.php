<?php
session_start();
require('connect.php');

// ตรวจสอบสิทธิ์การเข้าใช้งาน (employee เท่านั้น)
if (!isset($_SESSION['id_account']) || !isset($_SESSION['role_account']) || $_SESSION['role_account'] !== 'employee') {
    die(header('Location: form-login.php'));
}

$id_account = $_SESSION['id_account'];
$query_show = "SELECT username_account, role_account, images_account FROM account WHERE id_account = '$id_account'";
$call_back_show = mysqli_query($connect, $query_show);
$result_show = mysqli_fetch_assoc($call_back_show);

// สำหรับ active menu
$current_page_file = basename($_SERVER['SCRIPT_NAME']);
switch ($current_page_file) {
    case 'employee.php':
        $currentPage = 'dashboard';
        break;
    case 'room-management.php':
        $currentPage = 'manage_room';
        break;
    case 'booking-management.php':
        $currentPage = 'booking';
        break;
    default:
        $currentPage = '';
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>จัดการห้องพัก - Employee</title>
    
    <!-- Font Awesome CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }
    
    body {
        font-family: 'Kanit', sans-serif;
        background-color: #f8f9fa;
    }
    
    :root {
        --primary-color: #7b43ff;
        --light-primary-color: #f3f0ff;
        --text-color: #495057;
        --text-secondary-color: #6c757d;
        --sidebar-bg: #ffffff;
        --border-color: #e7e7e7;
        --header-bg: #f8f9fa;
        --hover-bg: #f8f9fa;
        --success-color: #28a745;
        --danger-color: #dc3545;
        --warning-color: #ffc107;
    }

    .sidebar {
        width: 280px;
        height: 100vh;
        position: fixed;
        top: 0;
        left: 0;
        background-color: var(--sidebar-bg);
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
        border-right: 1px solid var(--border-color);
        box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        overflow: hidden;
        z-index: 1000;
    }

    .sidebar-header { 
        padding: 20px; 
        margin-bottom: 0; 
        display: flex; 
        align-items: center;
        background: var(--header-bg);
        border-bottom: 1px solid var(--border-color);
        flex-shrink: 0;
    }

    .logo { 
        display: flex; 
        align-items: center; 
        font-size: 20px; 
        font-weight: 700; 
        color: #343a40; 
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .logo:hover {
        color: var(--primary-color);
        transform: translateY(-1px);
    }

    .logo i { 
        color: var(--primary-color); 
        font-size: 28px; 
        margin-right: 12px;
        transition: all 0.3s ease;
    }

    .user-profile { 
        display: flex; 
        align-items: center; 
        padding: 20px; 
        margin-bottom: 0; 
        border-bottom: 1px solid var(--border-color);
        background: var(--header-bg);
        flex-shrink: 0;
    }

    .user-avatar { 
        width: 50px; 
        height: 50px; 
        border-radius: 50%; 
        background: linear-gradient(135deg, var(--primary-color), #9c6fff);
        color: white; 
        display: flex; 
        align-items: center; 
        justify-content: center; 
        font-size: 24px; 
        margin-right: 15px;
        box-shadow: 0 4px 15px rgba(123, 67, 255, 0.3);
        transition: all 0.3s ease;
    }

    .user-avatar:hover {
        transform: scale(1.05);
        box-shadow: 0 6px 20px rgba(123, 67, 255, 0.4);
    }

    .user-info h5 { 
        margin: 0; 
        font-weight: 700; 
        font-size: 16px;
        color: #343a40;
    }

    .user-info p { 
        margin: 0; 
        font-size: 14px; 
        color: var(--text-secondary-color);
    }

    .sidebar-content {
        flex: 1;
        overflow-y: auto;
        padding: 0;
    }

    .sidebar-content::-webkit-scrollbar {
        width: 6px;
    }

    .sidebar-content::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }

    .sidebar-content::-webkit-scrollbar-thumb {
        background: var(--primary-color);
        border-radius: 3px;
    }

    .sidebar-content::-webkit-scrollbar-thumb:hover {
        background: #6a3df0;
    }

    .sidebar-nav { 
        list-style: none; 
        padding: 0; 
        margin: 0;
    }

    .nav-header { 
        padding: 20px 20px 10px; 
        font-size: 12px; 
        font-weight: 700; 
        color: #adb5bd; 
        text-transform: uppercase;
        letter-spacing: 1px;
        display: flex;
        align-items: center;
        background: var(--header-bg);
        border-bottom: 1px solid var(--border-color);
    }

    .nav-header i {
        margin-right: 10px;
        font-size: 14px;
        color: var(--primary-color);
    }

    .sidebar-nav li a { 
        display: flex; 
        align-items: center; 
        padding: 15px 20px; 
        margin: 2px 10px; 
        color: var(--text-color); 
        text-decoration: none; 
        font-size: 15px; 
        font-weight: 500; 
        border-radius: 10px; 
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .sidebar-nav li a::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        width: 0;
        background: linear-gradient(90deg, var(--primary-color), #9c6fff);
        transition: width 0.3s ease;
        z-index: -1;
    }

    .sidebar-nav li a:hover::before {
        width: 100%;
    }

    .sidebar-nav li a i { 
        width: 24px; 
        margin-right: 15px; 
        text-align: center; 
        font-size: 18px; 
        color: var(--text-secondary-color); 
        transition: all 0.3s ease;
    }

    .sidebar-nav li a span {
        transition: all 0.3s ease;
    }

    .sidebar-nav li.active a { 
        background: linear-gradient(135deg, var(--primary-color), #9c6fff);
        color: #fff; 
        font-weight: 600;
        box-shadow: 0 4px 15px rgba(123, 67, 255, 0.3);
        transform: translateX(5px);
    }

    .sidebar-nav li.active a i { 
        color: #fff;
        transform: scale(1.1);
    }

    .sidebar-nav li a:hover { 
        color: #fff;
        transform: translateX(5px);
    }

    .sidebar-nav li a:hover i { 
        color: #fff;
        transform: scale(1.1);
    }

    .sidebar-nav li.active a:hover { 
        background: linear-gradient(135deg, var(--primary-color), #9c6fff);
        color: #fff; 
        cursor: default;
        transform: translateX(5px);
    }

    .logout-item { 
        margin-top: 20px;
        padding: 0 10px 20px;
    }

    .logout-item a {
        background: linear-gradient(135deg, #dc3545, #e74c3c);
        color: white;
        border-radius: 10px;
        margin: 0;
        font-weight: 600;
        box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
    }

    .logout-item a:hover {
        background: linear-gradient(135deg, #c82333, #d63384);
        transform: translateX(5px);
    }

    .logout-item a i {
        color: white;
    }

    /* Main content area */
    .main-content {
        margin-left: 280px;
        padding: 20px;
        min-height: 100vh;
        background-color: #f8f9fa;
    }

    .content-header {
        background: white;
        padding: 30px;
        border-radius: 15px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }

    .content-header h1 {
        color: var(--primary-color);
        font-size: 28px;
        font-weight: 700;
        margin-bottom: 10px;
    }

    .content-header p {
        color: var(--text-secondary-color);
        font-size: 16px;
        margin: 0;
    }

    /* Room Management Styles */
    .room-container {
        background: white;
        border-radius: 15px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        padding: 30px;
        margin-bottom: 30px;
    }

    .room-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
    }

    .add-room-btn {
        background: linear-gradient(135deg, var(--primary-color), #9c6fff);
        color: white;
        border: none;
        padding: 12px 25px;
        border-radius: 10px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(123, 67, 255, 0.3);
    }

    .add-room-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(123, 67, 255, 0.4);
    }

    .room-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 20px;
    }

    .room-table th,
    .room-table td {
        padding: 15px;
        text-align: left;
        border-bottom: 1px solid var(--border-color);
    }

    .room-table th {
        background: var(--light-primary-color);
        color: var(--primary-color);
        font-weight: 600;
        font-size: 14px;
    }

    .room-table tr:hover {
        background: var(--hover-bg);
    }

    .status-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
    }

    .status-available {
        background: #d4edda;
        color: var(--success-color);
    }

    .status-occupied {
        background: #f8d7da;
        color: var(--danger-color);
    }

    .action-buttons {
        display: flex;
        gap: 8px;
    }

    .btn-edit {
        background: var(--warning-color);
        color: #212529;
        border: none;
        padding: 6px 12px;
        border-radius: 6px;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .btn-delete {
        background: var(--danger-color);
        color: white;
        border: none;
        padding: 6px 12px;
        border-radius: 6px;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .btn-edit:hover,
    .btn-delete:hover {
        transform: translateY(-1px);
    }

    /* Modal Styles */
    .modal {
        display: none;
        position: fixed;
        z-index: 2000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.5);
    }

    .modal-content {
        background-color: white;
        margin: 5% auto;
        padding: 30px;
        border-radius: 15px;
        width: 90%;
        max-width: 500px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        animation: modalSlideIn 0.3s ease;
    }

    @keyframes modalSlideIn {
        from {
            opacity: 0;
            transform: translateY(-50px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 25px;
        padding-bottom: 15px;
        border-bottom: 1px solid var(--border-color);
    }

    .modal-header h2 {
        color: var(--primary-color);
        font-size: 24px;
        font-weight: 700;
    }

    .close {
        color: var(--text-secondary-color);
        font-size: 28px;
        font-weight: bold;
        cursor: pointer;
        transition: color 0.3s ease;
    }

    .close:hover {
        color: var(--danger-color);
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
        color: var(--text-color);
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        width: 100%;
        padding: 12px 15px;
        border: 2px solid var(--border-color);
        border-radius: 8px;
        font-size: 14px;
        transition: border-color 0.3s ease;
        font-family: 'Kanit', sans-serif;
    }

    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
        outline: none;
        border-color: var(--primary-color);
    }

    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px;
    }

    .modal-footer {
        display: flex;
        justify-content: flex-end;
        gap: 15px;
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid var(--border-color);
    }

    .btn-cancel {
        background: var(--text-secondary-color);
        color: white;
        border: none;
        padding: 12px 25px;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .btn-submit {
        background: linear-gradient(135deg, var(--primary-color), #9c6fff);
        color: white;
        border: none;
        padding: 12px 25px;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .btn-cancel:hover,
    .btn-submit:hover {
        transform: translateY(-1px);
    }

    /* Alert Messages */
    .alert {
        padding: 15px 20px;
        border-radius: 8px;
        margin-bottom: 20px;
        font-weight: 500;
    }

    .alert-success {
        background: #d4edda;
        color: var(--success-color);
        border: 1px solid #c3e6cb;
    }

    .alert-danger {
        background: #f8d7da;
        color: var(--danger-color);
        border: 1px solid #f5c6cb;
    }

    /* Responsive design */
    @media (max-width: 768px) {
        .sidebar {
            width: 100%;
            height: auto;
            position: relative;
        }
        
        .sidebar-content {
            max-height: 400px;
        }
        
        .main-content {
            margin-left: 0;
        }

        .form-row {
            grid-template-columns: 1fr;
        }

        .room-header {
            flex-direction: column;
            gap: 15px;
            align-items: stretch;
        }
    }
    </style>
</head>
<body>

<!-- Sidebar สำหรับ employee -->
<aside class="sidebar">
    <div class="sidebar-header">
        <a href="employee.php" class="logo">
            <i class="fa-solid fa-hotel"></i>
            <span>ระบบจัดการรีสอร์ท</span>
        </a>
    </div>
    <div class="user-profile">
        <div class="user-avatar">
            <i class="fa-solid fa-user"></i>
        </div>
        <div class="user-info">
            <h5><?php echo htmlspecialchars($result_show['username_account']); ?></h5>
            <p><?php echo htmlspecialchars($result_show['role_account']); ?></p>
        </div>
    </div>
    <div class="sidebar-content">
        <ul class="sidebar-nav">
            <li class="nav-header">
                <i class="fa-solid fa-bed"></i>
                <span>ห้องพัก</span>
            </li>
            <li class="<?php if ($currentPage == 'dashboard') echo 'active'; ?>">
                <a href="employee.php"><i class="fa-solid fa-home"></i><span>ห้องพัก</span></a>
            </li>
            <li class="<?php if ($currentPage == 'manage_room') echo 'active'; ?>">
                <a href="room-management.php"><i class="fa-solid fa-edit"></i><span>แก้ไขห้องพัก</span></a>
            </li>
            <li class="<?php if ($currentPage == 'booking') echo 'active'; ?>">
                <a href="booking-management.php"><i class="fa-solid fa-check-circle"></i><span>อนุมัติการจอง</span></a>
            </li>
            <li class="<?php if ($currentPage == 'booking') echo 'active'; ?>">
                <a href="booking-management.php"><i class="fa-solid fa-key"></i><span>คืนห้องพัก</span></a>
            </li>
            <li class="<?php if ($currentPage == 'booking') echo 'active'; ?>">
                <a href="booking-management.php"><i class="fa-solid fa-history"></i><span>ประวัติการจอง</span></a>
            </li>
            
            <li class="nav-header">
                <i class="fa-solid fa-coffee"></i>
                <span>เครื่องดื่ม</span>
            </li>
            <li class="<?php if ($currentPage == 'manage_room') echo 'active'; ?>">
                <a href="employee.php"><i class="fa-solid fa-mug-hot"></i><span>เครื่องดื่ม</span></a>
            </li>
            <li class="<?php if ($currentPage == 'manage_room') echo 'active'; ?>">
                <a href="employee.php"><i class="fa-solid fa-edit"></i><span>แก้ไขเครื่องดื่ม</span></a>
            </li>
            
            <li class="nav-header">
                <i class="fa-solid fa-receipt"></i>
                <span>ใบเสร็จ</span>
            </li>
            <li class="<?php if ($currentPage == 'manage_room') echo 'active'; ?>">
                <a href="employee.php"><i class="fa-solid fa-print"></i><span>ออกใบเสร็จ</span></a>
            </li>
            
            <li class="nav-header">
                <i class="fa-solid fa-user-cog"></i>
                <span>เกี่ยวกับฉัน</span>
            </li>
            <li class="<?php if ($currentPage == 'manage_room') echo 'active'; ?>">
                <a href="employee.php"><i class="fa-solid fa-lock"></i><span>เปลี่ยนรหัสผ่าน</span></a>
            </li>
            
            <li class="logout-item">
                <a href="form-login.php?logout=1"><i class="fa-solid fa-sign-out-alt"></i><span>ออกจากระบบ</span></a>
            </li>
        </ul>
    </div>
</aside>

<!-- Main Content -->
<div class="main-content">
    <div class="content-header">
        <h1><i class="fa-solid fa-bed"></i> จัดการห้องพัก</h1>
        <p>เพิ่ม แก้ไข และลบข้อมูลห้องพักในระบบ</p>
    </div>

    <?php
    // แสดงข้อความแจ้งเตือน
    if (isset($_SESSION['message'])) {
        $message = $_SESSION['message'];
        $message_type = $_SESSION['message_type'] ?? 'success';
        echo "<div class='alert alert-{$message_type}'>{$message}</div>";
        unset($_SESSION['message']);
        unset($_SESSION['message_type']);
    }
    ?>

    <div class="room-container">
        <div class="room-header">
            <h2><i class="fa-solid fa-list"></i> รายการห้องพัก</h2>
            <button class="add-room-btn" onclick="showAddRoomModal()">
                <i class="fa-solid fa-plus"></i> เพิ่มห้องพักใหม่
            </button>
        </div>

        <table class="room-table">
            <thead>
                <tr>
                    <th>หมายเลขห้อง</th>
                    <th>ประเภทห้อง</th>
                    <th>ราคา (บาท/คืน)</th>
                    <th>สถานะ</th>
                    <th>รายละเอียด</th>
                    <th>จัดการ</th>
                </tr>
            </thead>
            <tbody>
                <?php
                $query = "SELECT * FROM room ORDER BY room_number ASC";
                $result = mysqli_query($connect, $query);
                
                if (mysqli_num_rows($result) > 0) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $status_class = ($row['status'] == 'available') ? 'status-available' : 'status-occupied';
                        $status_text = ($row['status'] == 'available') ? 'ว่าง' : 'ไม่ว่าง';
                        
                        echo "<tr>
                            <td><strong>{$row['room_number']}</strong></td>
                            <td>{$row['room_type']}</td>
                            <td>" . number_format($row['price']) . "</td>
                            <td><span class='status-badge {$status_class}'>{$status_text}</span></td>
                            <td>" . substr($row['description'], 0, 50) . (strlen($row['description']) > 50 ? '...' : '') . "</td>
                            <td>
                                <div class='action-buttons'>
                                    <button class='btn-edit' onclick='editRoom({$row['id_room']})'>
                                        <i class='fa-solid fa-edit'></i>
                                    </button>
                                    <button class='btn-delete' onclick='deleteRoom({$row['id_room']})'>
                                        <i class='fa-solid fa-trash'></i>
                                    </button>
                                </div>
                            </td>
                        </tr>";
                    }
                } else {
                    echo "<tr><td colspan='6' style='text-align: center; padding: 30px; color: var(--text-secondary-color);'>ยังไม่มีข้อมูลห้องพัก</td></tr>";
                }
                ?>
            </tbody>
        </table>
    </div>
</div>

<!-- Modal สำหรับเพิ่ม/แก้ไขห้องพัก -->
<div id="roomModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h2 id="modalTitle">เพิ่มห้องพักใหม่</h2>
            <span class="close" onclick="closeModal()">&times;</span>
        </div>
        
        <form id="roomForm" action="process-room.php" method="POST" enctype="multipart/form-data">
            <input type="hidden" id="room_id" name="room_id" value="">
            <input type="hidden" id="action" name="action" value="add">
            
            <div class="form-row">
                <div class="form-group">
                    <label for="room_number">หมายเลขห้อง *</label>
                    <input type="text" id="room_number" name="room_number" required>
                </div>
                <div class="form-group">
                    <label for="room_type">ประเภทห้อง *</label>
                    <select id="room_type" name="room_type" required>
                        <option value="">เลือกประเภทห้อง</option>
                        <option value="Standard">Standard</option>
                        <option value="Deluxe">Deluxe</option>
                        <option value="Suite">Suite</option>
                        <option value="Family">Family</option>
                        <option value="VIP">VIP</option>
                    </select>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="price">ราคา (บาท/คืน) *</label>
                    <input type="number" id="price" name="price" min="0" step="0.01" required>
                </div>
                <div class="form-group">
                    <label for="status">สถานะ *</label>
                    <select id="status" name="status" required>
                        <option value="available">ว่าง</option>
                        <option value="occupied">ไม่ว่าง</option>
                    </select>
                </div>
            </div>
            
            <div class="form-group">
                <label for="description">รายละเอียดห้อง</label>
                <textarea id="description" name="description" rows="4" placeholder="อธิบายรายละเอียดของห้องพัก..."></textarea>
            </div>
            
            <div class="form-group">
                <label for="room_image">รูปภาพห้อง (ไม่บังคับ)</label>
                <input type="file" id="room_image" name="room_image" accept="image/*">
            </div>
            
            <div class="modal-footer">
                <button type="button" class="btn-cancel" onclick="closeModal()">ยกเลิก</button>
                <button type="submit" class="btn-submit">บันทึก</button>
            </div>
        </form>
    </div>
</div>

<script>
// ฟังก์ชันแสดง Modal เพิ่มห้อง
function showAddRoomModal() {
    document.getElementById('modalTitle').textContent = 'เพิ่มห้องพักใหม่';
    document.getElementById('roomForm').reset();
    document.getElementById('action').value = 'add';
    document.getElementById('room_id').value = '';
    document.getElementById('roomModal').style.display = 'block';
}

// ฟังก์ชันแก้ไขห้อง
function editRoom(roomId) {
    // ใช้ AJAX เพื่อดึงข้อมูลห้อง
    fetch('get-room.php?id=' + roomId)
        .then(response => response.json())
        .then(data => {
            document.getElementById('modalTitle').textContent = 'แก้ไขห้องพัก';
            document.getElementById('action').value = 'edit';
            document.getElementById('room_id').value = data.id_room;
            document.getElementById('room_number').value = data.room_number;
            document.getElementById('room_type').value = data.room_type;
            document.getElementById('price').value = data.price;
            document.getElementById('status').value = data.status;
            document.getElementById('description').value = data.description;
            document.getElementById('roomModal').style.display = 'block';
        })
        .catch(error => {
            alert('เกิดข้อผิดพลาดในการดึงข้อมูลห้อง');
        });
}

// ฟังก์ชันลบห้อง
function deleteRoom(roomId) {
    if (confirm('คุณต้องการลบห้องพักนี้หรือไม่?')) {
        fetch('process-room.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=delete&room_id=' + roomId
        })
        .then(response => response.text())
        .then(data => {
            location.reload();
        })
        .catch(error => {
            alert('เกิดข้อผิดพลาดในการลบห้องพัก');
        });
    }
}

// ฟังก์ชันปิด Modal
function closeModal() {
    document.getElementById('roomModal').style.display = 'none';
}

// ปิด Modal เมื่อคลิกนอก Modal
window.onclick = function(event) {
    var modal = document.getElementById('roomModal');
    if (event.target == modal) {
        modal.style.display = 'none';
    }
}

function getRoomTypeThai($type) {
    $types = [
        'Standard' => 'ห้องมาตรฐาน',
        'Deluxe' => 'ห้องดีลักซ์',
        'Suite' => 'ห้องสวีท',
        'Family' => 'ห้องครอบครัว',
        'VIP' => 'ห้องวีไอพี'
    ];
    return $types[$type] ?? $type;
}
</script>

</body>
</html>